package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req;

import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.BaseRequest;
import com.shouqianba.trade.fund.settlement.api.request.SettlementFlowCreateRequest;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAccountModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAmountModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementFlowBizInfoModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementTradeInfoModel;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2025/5/21 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class FundClearingSettlementFlowCreateRequest extends BaseRequest {

    /**
     * 流水类型:1-入账结算 2-分账结算
     */
    private Byte type;

    /**
     * 交易单号
     */
    private String transSn;

    /**
     * 业务订单号
     */
    private String orderSn;

    /**
     * 批次号
     */
    private Long batchId;

    /**
     * 资金池ID
     */
    private Long poolId;

    /**
     * 付款方信息
     */
    private AccountModel fromInfo;

    /**
     * 收款方信息
     */
    private AccountModel toInfo;

    /**
     * 金额信息
     */
    private AmountModel amount;

    /**
     * 交易信息
     */
    private TradeInfoModel tradeInfo;

    /**
     * 业务信息
     */
    private BizInfoModel bizInfo;

    @Getter
    @Builder
    @ToString
    @Jacksonized
    public static class AccountModel {
        private Byte type;
        private String brandSn;
        private String merchantSn;
        private String storeSn;
        private String channelMerchantSn;
    }

    @Getter
    @Builder
    @ToString
    @Jacksonized
    public static class AmountModel {
        private Long originAmount;
        private Long fee;
        private Long settleAmount;
    }

    @Getter
    @Builder
    @ToString
    @Jacksonized
    public static class TradeInfoModel {
        /**
         * 流水渠道： 1：收钱吧、2：标准收单接口、3：美团、4：饿了么、5、品牌上送、6：抖音
         */
        private Byte flowChannel;

        /**
         * 结算类型：1-结算、2-收款、3-退款、4-分账、5-分账回退
         */
        private Byte settleType;

        /**
         * 收单机构
         */
        private Integer acquiringCompany;

        /**
         * 支付方式
         */
        private Byte payWay;

        /**
         * 交易时间
         */
        private String tradeTime;

        /**
         * 渠道交易流水号
         */
        private String channelTransSn;

        /**
         * 渠道订单号
         */
        private String channelOrderSn;
    }

    @Getter
    @Builder
    @ToString
    @Jacksonized
    public static class BizInfoModel {

    }

    public SettlementFlowCreateRequest genSettlementFlowCreateRequest() {
        return SettlementFlowCreateRequest
                .builder()
                .type(this.type)
                .transSn(this.transSn)
                .orderSn(this.orderSn)
                .batchId(this.batchId)
                .poolId(this.poolId)
                .fromInfo(FundSettlementAccountModel
                        .builder()
                        .type(this.fromInfo.type)
                        .brandSn(this.fromInfo.brandSn)
                        .merchantSn(this.fromInfo.merchantSn)
                        .storeSn(this.fromInfo.storeSn)
                        .channelMerchantSn(this.fromInfo.channelMerchantSn)
                        .build())
                .toInfo(FundSettlementAccountModel
                        .builder()
                        .type(this.toInfo.type)
                        .brandSn(this.toInfo.brandSn)
                        .merchantSn(this.toInfo.merchantSn)
                        .storeSn(this.toInfo.storeSn)
                        .channelMerchantSn(this.toInfo.channelMerchantSn)
                        .build())
                .amount(FundSettlementAmountModel
                        .builder()
                        .originAmount(this.amount.originAmount)
                        .fee(this.amount.fee)
                        .settleAmount(this.amount.settleAmount)
                        .build())
                .tradeInfo(FundSettlementTradeInfoModel
                        .builder()
                        .flowChannel(this.tradeInfo.flowChannel)
                        .settleType(this.tradeInfo.settleType)
                        .acquiringCompany(this.tradeInfo.acquiringCompany)
                        .payWay(this.tradeInfo.payWay)
                        .tradeTime(this.tradeInfo.tradeTime)
                        .channelTransSn(this.tradeInfo.channelTransSn)
                        .channelOrderSn(this.tradeInfo.channelOrderSn)
                        .build())
                .bizInfo(FundSettlementFlowBizInfoModel
                        .builder()
                        .build())
                .build();
    }
}
