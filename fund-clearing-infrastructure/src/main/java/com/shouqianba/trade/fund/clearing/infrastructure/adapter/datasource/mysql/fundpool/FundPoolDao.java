package com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool;

import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.model.FundPoolQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.po.FundPoolPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Mapper
public interface FundPoolDao {
    int insert(FundPoolPO po);

    int batchInsert(List<FundPoolPO> pos);

    int update(FundPoolPO po);

    FundPoolPO select(FundPoolQuery query);

    List<FundPoolPO> batchSelect(FundPoolQuery query);

    Long count(FundPoolQuery fundPoolQuery);
}
