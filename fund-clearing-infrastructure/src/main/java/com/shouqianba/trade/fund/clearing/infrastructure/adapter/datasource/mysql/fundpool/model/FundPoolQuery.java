package com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.model;

import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query.FundPoolAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.BaseQuery;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Setter
@SuperBuilder(toBuilder = true)
public class FundPoolQuery extends BaseQuery {

    private Long id;
    private Long batchId;
    private Byte payeeType;
    private String brandSn;
    private String merchantSn;
    private String storeSn;
    private Integer acquiringCompany;
    private Byte billSource;
    private LocalDate settleDate;
    private Byte status;
    private List<Byte> statusList; // 多个状态查询

    public static FundPoolQuery genFundPoolQuery(FundPoolAggrQuery aggrQuery) {
        if (Objects.isNull(aggrQuery)) {
            return null;
        }

        FundPoolQuery.FundPoolQueryBuilder<?, ?> builder = FundPoolQuery.builder();

        if (Objects.nonNull(aggrQuery.getId())) {
            builder.id(aggrQuery.getId());
        }
        if (Objects.nonNull(aggrQuery.getBatchId())) {
            builder.batchId(aggrQuery.getBatchId());
        }
        if (Objects.nonNull(aggrQuery.getPayeeType())) {
            builder.payeeType(aggrQuery.getPayeeType().getCode());
        }
        if (Objects.nonNull(aggrQuery.getBrandSn())) {
            builder.brandSn(aggrQuery.getBrandSn());
        }
        if (Objects.nonNull(aggrQuery.getMerchantSn())) {
            builder.merchantSn(aggrQuery.getMerchantSn());
        }
        if (Objects.nonNull(aggrQuery.getStoreSn())) {
            builder.storeSn(aggrQuery.getStoreSn());
        }

        if (Objects.nonNull(aggrQuery.getAcquiringCompany())) {
            builder.acquiringCompany(aggrQuery.getAcquiringCompany());
        }

        if (Objects.nonNull(aggrQuery.getBillSource())) {
            builder.billSource(aggrQuery.getBillSource().getCode());
        }
        if (Objects.nonNull(aggrQuery.getSettleDate())) {
            builder.settleDate(aggrQuery.getSettleDate());
        }
        if (Objects.nonNull(aggrQuery.getStatus())) {
            builder.status(aggrQuery.getStatus().getCode());
        }
        if (Objects.nonNull(aggrQuery.getStatusList()) && !aggrQuery.getStatusList().isEmpty()) {
            List<Byte> statusCodeList = aggrQuery.getStatusList().stream()
                    .map(FundPoolStatusEnum::getCode)
                    .collect(Collectors.toList());
            builder.statusList(statusCodeList);
        }
        if (Objects.nonNull(aggrQuery.getSortField())) {
            builder.sortField(aggrQuery.getSortField());
            builder.isDesc(aggrQuery.isDesc());
        }
        if (Objects.nonNull(aggrQuery.getQuerySize())) {
            builder.querySize(aggrQuery.getQuerySize());
        }

        return builder.build();
    }
}
