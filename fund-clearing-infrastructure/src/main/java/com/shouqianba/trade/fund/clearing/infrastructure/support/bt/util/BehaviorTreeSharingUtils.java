package com.shouqianba.trade.fund.clearing.infrastructure.support.bt.util;


import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.refundsharing.GenRefundSharingTransactionActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.refundsharing.RefundSharingInfoShareActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.refundsharing.TransactionSharingSettleActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.refundsharing.UpdatePayBillActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.GenSharingBookActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.PoolSharingSettleActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.PoolWithDrawActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.SharingInfoShareActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.bill.GenBillSharingTransactionActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.bill.api.UpdateBillApiPaySharingTransactionActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.bill.rule.UpdateBillRulePaySharingTransactionActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing.wallet.GenWalletPaySharingTransactionActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.refundsharing.IsNotSettledBillCanRefundPredicate;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.refundsharing.IsSettledBillCanRefundPredicate;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.sharing.IsAPISharingConditionPredicate;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.sharing.IsBaseOrderConditionPredicate;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.sharing.IsBaseWalletConditionPredicate;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.sharing.IsRuleSharingConditionPredicate;
import com.wosai.general.ds.bt.RootNode;
import com.wosai.general.ds.bt.composite.StrictSelectorNode;
import com.wosai.general.ds.bt.composite.StrictSequenceNode;
import com.wosai.general.ds.bt.leaf.DefaultActionNode;
import com.wosai.general.ds.bt.leaf.DefaultConditionNode;

import java.util.Map;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
public class BehaviorTreeSharingUtils {

    public static void buildSharingBehaviorTree() {
        var rootNode = new RootNode.RootNodeBuilder<>()
                .id("0")
                .alias("根节点")
                .description("根节点")
                .name("根节点")
                .build();


        var INfoShareSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("100")
                .alias("信息共享严格顺序节点")
                .description("信息共享严格顺序节点")
                .name("信息共享严格顺序节点")
                .build();

        var InfoShareNode = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("101")
                .name("信息共享")
                .description("信息共享")
                .build();
        InfoShareNode.addAction(new SharingInfoShareActionFunction(
                Map.of("poolIdOutKey", "poolId",
                        "sharingRuleIdOutKey", "sharingRuleId")));

        var sharingNode = new StrictSelectorNode.StrictSelectorNodeBuilder<>()
                .id("1")
                .alias("分账节点")
                .description("分账节点")
                .name("分账节点")
                .build();

        var orderSharingNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("2")
                .alias("订单分账节点")
                .description("订单分账节点")
                .name("订单分账节点")
                .build();

        var walletSharingSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("3")
                .alias("余额分账节点")
                .description("余额分账节点")
                .name("余额分账节点")
                .build();

        var isOrderSharingNode = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("4")
                .alias("是否订单分账")
                .description("是否订单分账")
                .name("是否订单分账")
                .build();
        isOrderSharingNode.addCondition(new IsBaseOrderConditionPredicate(
                Map.of("sharingRuleIdInKey", "sharingRuleId")));

        var orderSharingSelectNode = new StrictSelectorNode.StrictSelectorNodeBuilder<>()
                .id("5")
                .alias("订单分账选择节点")
                .description("订单分账选择节点")
                .name("订单分账选择节点")
                .build();

        var genOrderPaySharingTransactionNode = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("6")
                .alias("生成订单分账交易")
                .name("生成订单分账交易")
                .description("生成订单分账交易")
                .build();
        genOrderPaySharingTransactionNode.addAction(new GenBillSharingTransactionActionFunction(
                Map.of("sharingRuleIdInKey", "sharingRuleId",
                        "poolIdInKey", "poolId")));

        var orderApiSharingSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("7")
                .alias("订单API分账")
                .description("订单API分账")
                .name("订单API分账")
                .build();

        var isOrderApiConditionNode = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("8")
                .alias("是否API分账")
                .description("是否API分账")
                .name("是否API分账")
                .build();
        isOrderApiConditionNode.addCondition(new IsAPISharingConditionPredicate(
                Map.of("sharingRuleIdInKey", "sharingRuleId")));

        var updateOrderPayApiSharingTransactionNode =
                new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                        .id("9")
                        .alias("订单API分账交易")
                        .name("订单API分账交易")
                        .description("订单API分账交易")
                        .build();
        updateOrderPayApiSharingTransactionNode.addAction(
                new UpdateBillApiPaySharingTransactionActionFunction(
                        Map.of("sharingRuleIdInKey", "sharingRuleId",
                                "poolIdInKey", "poolId")));

        var orderRuleSharingSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("11")
                .alias("订单规则分账")
                .description("订单规则分账")
                .name("订单规则分账")
                .build();

        var isOrderRuleConditionNode = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("12")
                .alias("是否规则分账")
                .description("是否规则分账")
                .name("是否规则分账")
                .build();
        isOrderRuleConditionNode.addCondition(new IsRuleSharingConditionPredicate(
                Map.of("sharingRuleIdInKey", "sharingRuleId")));

        var updateOrderPayRuleSharingTransactionNode =
                new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                        .id("13")
                        .alias("订单规则分账交易")
                        .name("订单规则分账交易")
                        .description("订单规则分账交易")
                        .build();
        updateOrderPayRuleSharingTransactionNode.addAction(
                new UpdateBillRulePaySharingTransactionActionFunction(
                        Map.of("sharingRuleIdInKey", "sharingRuleId",
                                "poolIdInKey", "poolId")));

        var isWalletSharingNode = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("15")
                .alias("是否余额分账")
                .description("是否余额分账")
                .name("是否余额分账")
                .build();
        isWalletSharingNode.addCondition(new IsBaseWalletConditionPredicate(
                Map.of("sharingRuleIdInKey", "sharingRuleId")));

        var genWalletPaySharingTransactionNode =
                new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                        .id("16")
                        .alias("生成余额分账交易")
                        .name("生成余额分账交易")
                        .description("生成余额分账交易")
                        .build();
        genWalletPaySharingTransactionNode.addAction(new GenWalletPaySharingTransactionActionFunction(
                Map.of("sharingRuleIdInKey", "sharingRuleId",
                        "poolIdInKey", "poolId")));

        var genClearingBookNode1 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("18")
                .alias("生成分账账单")
                .name("生成分账账单")
                .description("生成分账账单")
                .build();
        genClearingBookNode1.addAction(new GenSharingBookActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var genClearingBookNode2 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("19")
                .alias("生成分账账单")
                .name("生成分账账单")
                .description("生成分账账单")
                .build();
        genClearingBookNode2.addAction(new GenSharingBookActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var genClearingBookNode3 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("20")
                .alias("生成分账账单")
                .name("生成分账账单")
                .description("生成分账账单")
                .build();
        genClearingBookNode3.addAction(new GenSharingBookActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var SettleNode1 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("21")
                .alias("生成分账账单")
                .name("生成分账账单")
                .description("生成分账账单")
                .build();
        SettleNode1.addAction(new PoolSharingSettleActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var SettleNode2 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("22")
                .alias("生成分账账单")
                .name("生成分账账单")
                .description("生成分账账单")
                .build();
        SettleNode2.addAction(new PoolSharingSettleActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var SettleNode3 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("23")
                .alias("生成分账账单")
                .name("生成分账账单")
                .description("生成分账账单")
                .build();
        SettleNode3.addAction(new PoolSharingSettleActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var PoolSettlement1 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("24")
                .alias("池推送结算")
                .name("池推送结算")
                .description("池推送结算")
                .build();
        PoolSettlement1.addAction(new PoolSharingSettleActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var PoolSettlement2 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("25")
                .alias("池推送结算")
                .name("池推送结算")
                .description("池推送结算")
                .build();
        PoolSettlement2.addAction(new PoolSharingSettleActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var PoolSettlement3 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("26")
                .alias("池推送结算")
                .name("池推送结算")
                .description("池推送结算")
                .build();
        PoolSettlement3.addAction(new PoolSharingSettleActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var WithdrawNode1 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("27")
                .alias("发起全额提现")
                .name("发起全额提现")
                .description("发起全额提现")
                .build();
        WithdrawNode1.addAction(new PoolWithDrawActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var WithdrawNode2 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("28")
                .alias("发起全额提现")
                .name("发起全额提现")
                .description("发起全额提现")
                .build();
        WithdrawNode2.addAction(new PoolWithDrawActionFunction(
                Map.of("poolIdInKey", "poolId")));

        var WithdrawNode3 = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("29")
                .alias("发起全额提现")
                .name("发起全额提现")
                .description("发起全额提现")
                .build();
        WithdrawNode3.addAction(new PoolWithDrawActionFunction(
                Map.of("poolIdInKey", "poolId")));

        rootNode.addChild(INfoShareSequenceNode);
        INfoShareSequenceNode.addChild(InfoShareNode);
        INfoShareSequenceNode.addChild(sharingNode);
        sharingNode.addChild(orderSharingNode);
        sharingNode.addChild(walletSharingSequenceNode);
        orderSharingNode.addChild(isOrderSharingNode);
        orderSharingNode.addChild(genOrderPaySharingTransactionNode);
        orderSharingNode.addChild(orderSharingSelectNode);
        orderSharingSelectNode.addChild(orderApiSharingSequenceNode);
        orderSharingSelectNode.addChild(orderRuleSharingSequenceNode);
        orderApiSharingSequenceNode.addChild(isOrderApiConditionNode);
        orderApiSharingSequenceNode.addChild(updateOrderPayApiSharingTransactionNode);
        orderApiSharingSequenceNode.addChild(genClearingBookNode1);
        orderApiSharingSequenceNode.addChild(SettleNode1);
        orderApiSharingSequenceNode.addChild(PoolSettlement1);
        orderApiSharingSequenceNode.addChild(WithdrawNode1);
        orderRuleSharingSequenceNode.addChild(isOrderRuleConditionNode);
        orderRuleSharingSequenceNode.addChild(updateOrderPayRuleSharingTransactionNode);
        orderRuleSharingSequenceNode.addChild(genClearingBookNode2);
        orderRuleSharingSequenceNode.addChild(SettleNode2);
        orderRuleSharingSequenceNode.addChild(PoolSettlement2);
        orderRuleSharingSequenceNode.addChild(WithdrawNode2);
        walletSharingSequenceNode.addChild(isWalletSharingNode);
        walletSharingSequenceNode.addChild(genWalletPaySharingTransactionNode);
        walletSharingSequenceNode.addChild(genClearingBookNode3);
        walletSharingSequenceNode.addChild(SettleNode3);
        walletSharingSequenceNode.addChild(PoolSettlement3);
        walletSharingSequenceNode.addChild(WithdrawNode3);

        System.out.println(rootNode.toJsonString());
    }


    public static void buildRefundSharingBehaviorTree() {
        var rootNode = new RootNode.RootNodeBuilder<>()
                .id("0")
                .alias("根节点")
                .description("根节点")
                .name("根节点")
                .build();

        var INfoShareSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("100")
                .alias("信息共享严格顺序节点")
                .description("信息共享严格顺序节点")
                .name("信息共享严格顺序节点")
                .build();

        var InfoShareNode = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("101")
                .name("信息共享")
                .description("信息共享")
                .build();
        InfoShareNode.addAction(new RefundSharingInfoShareActionFunction(
                Map.of("billIdOutKey", "billId",
                        "sharingRuleIdOutKey", "sharingRuleId")));


        var sharingNode = new StrictSelectorNode.StrictSelectorNodeBuilder<>()
                .id("1")
                .alias("分账节点")
                .description("分账节点")
                .name("分账节点")
                .build();

        var GenRefund = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("2")
                .alias("退款生成节点")
                .description("退款生成节点")
                .name("退款生成节点")
                .build();

        var UpdateBill = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("3")
                .alias("账单更新节点")
                .description("账单更新节点")
                .name("账单更新节点")
                .build();

        var isGen = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("4")
                .alias("是否已结算账单可退款")
                .description("是否已结算账单可退款")
                .name("是否已结算账单可退款")
                .build();
        isGen.addCondition(new IsSettledBillCanRefundPredicate(
                Map.of("sharingRuleIdInKey", "sharingRuleId", "billIdInKey", "billId")));

        var isUpdate = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("5")
                .alias("是否未结算账单可退款")
                .description("是否未结算账单可退款")
                .name("是否未结算账单可退款")
                .build();
        isUpdate.addCondition(new IsNotSettledBillCanRefundPredicate(
                Map.of("sharingRuleIdInKey", "sharingRuleId", "billIdInKey", "billId")));

        var gen = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("6")
                .alias("生成退款分账交易")
                .name("生成退款分账交易")
                .description("生成退款分账交易")
                .build();
        gen.addAction(new GenRefundSharingTransactionActionFunction(
                Map.of("sharingRuleIdInKey", "sharingRuleId",
                        "billIdInKey", "billId", "transactionOutKey", "transactionId")));

        var settle = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("9")
                .alias("9")
                .name("9")
                .description("9")
                .build();
        settle.addAction(new TransactionSharingSettleActionFunction(
                Map.of("transactionInKey", "transactionId")));

        var update = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("7")
                .alias("更新支付账单")
                .name("更新支付账单")
                .description("更新支付账单")
                .build();
        update.addAction(new UpdatePayBillActionFunction(
                Map.of("sharingRuleIdInKey", "sharingRuleId", "billIdInKey", "billId")));


        rootNode.addChild(INfoShareSequenceNode);
        INfoShareSequenceNode.addChild(InfoShareNode);
        INfoShareSequenceNode.addChild(sharingNode);
        sharingNode.addChild(GenRefund);
        sharingNode.addChild(UpdateBill);
        GenRefund.addChild(isGen);
        GenRefund.addChild(gen);
        GenRefund.addChild(settle);
        UpdateBill.addChild(isUpdate);
        UpdateBill.addChild(update);
        System.out.println(rootNode.toJsonString());
    }

    public static void main(String[] args) {
        buildSharingBehaviorTree();
    }

}
