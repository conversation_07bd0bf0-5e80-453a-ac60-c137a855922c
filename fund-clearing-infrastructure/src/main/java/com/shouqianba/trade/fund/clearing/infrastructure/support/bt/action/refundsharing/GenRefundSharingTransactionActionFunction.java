package com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.refundsharing;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillAccountModel;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.FundPoolFlowDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.FundPoolFlowAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.FundPoolFlowAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowBusinessTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.vo.FundPoolFlowAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.vo.FundPoolFlowBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.FundPoolSharingTransactionDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.enums.FundPoolSharingTransactionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.FundPoolSharingTransactionAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.FundPoolSharingTransactionBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.ReceiverVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.FundPoolSharingTransBookDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.FundPoolSharingTransBookAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.FundPoolSharingTransBookAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.enums.FundPoolSharingTransBookStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.vo.FundPoolSharingTransBookAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.ReceiverDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.model.ReceiverAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.model.ReceiverAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.SharingRuleDomainService;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.SharingRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SharingBaseEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.FundTransactionTypeEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.annotation.FieldDescription;
import com.shouqianba.trade.fund.clearing.infrastructure.service.FundPoolSupportService;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.AbstractClearingFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.util.SpringContextUtils;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.AbstractPayload;
import com.shouqianba.trade.fund.clearing.infrastructure.support.service.ClearingAccountSupportService;
import com.wosai.general.ds.bt.Node;
import com.wosai.general.ds.bt.leaf.AbstractActionNode;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.support.TransactionTemplate;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2025/4/25 Time: 17:31
 */
@Slf4j
@JsonTypeName(GenRefundSharingTransactionActionFunction.TYPE)
public class GenRefundSharingTransactionActionFunction extends AbstractClearingFunction {
    protected static final String TYPE = "GenRefundSharingTransaction";

    protected final SharingRuleDomainService sharingRuleDomainService;
    protected final FundBillDomainRepository fundBillDomainRepository;
    protected final FundPoolSupportService fundPoolSupportService;
    protected final FundPoolSharingTransBookDomainRepository fundPoolSharingTransBookDomainRepository;
    protected final TransactionTemplate transactionTemplate;
    protected final FundPoolSharingTransactionDomainRepository transactionDomainRepository;
    protected final FundPoolFlowDomainRepository fundPoolFlowDomainRepository;
    protected final ReceiverDomainRepository receiverDomainRepository;
    protected final ClearingAccountSupportService clearingAccountSupportService;
    @JsonIgnore
    protected final transient GenRefundSharingTransactionActionFunctionPayload payloadVO
            = AbstractPayload.convertPayload(getPayload(), GenRefundSharingTransactionActionFunctionPayload.class);

    @JsonCreator
    public GenRefundSharingTransactionActionFunction(
            @JsonProperty("payload") Map<String, Object> payload) {
        super(TYPE, payload);
        sharingRuleDomainService = SpringContextUtils.getBean(SharingRuleDomainService.class);
        fundBillDomainRepository = SpringContextUtils.getBean(FundBillDomainRepository.class);
        fundPoolSupportService = SpringContextUtils.getBean(FundPoolSupportService.class);
        fundPoolSharingTransBookDomainRepository = SpringContextUtils.getBean(FundPoolSharingTransBookDomainRepository.class);
        transactionTemplate = SpringContextUtils.getBean("transactionTemplate", TransactionTemplate.class);
        transactionDomainRepository = SpringContextUtils.getBean(FundPoolSharingTransactionDomainRepository.class);
        fundPoolFlowDomainRepository = SpringContextUtils.getBean(FundPoolFlowDomainRepository.class);
        receiverDomainRepository = SpringContextUtils.getBean(ReceiverDomainRepository.class);
        clearingAccountSupportService = SpringContextUtils.getBean(ClearingAccountSupportService.class);
    }


    @Override
    public Node.NodeStatus doApply(ClearingCustomBlackboard blackboard, AbstractActionNode<?
            , ClearingCustomBlackboard> actionNode) {


        FundBillAggrRoot fundBillAggrRoot = fundBillDomainRepository.query(FundBillAggrQuery.builder()
                .id(blackboard.getAggrRootIdNotNull(payloadVO.getBillIdInKey()))
                .status(FundBillStatusEnum.ENTERED.getCode())
                .build());
        fundBillAggrRoot.checkExist();

        FundPoolAggrRoot fundPoolAggrRoot = fundPoolSupportService.queryFundPoolByBill(fundBillAggrRoot);
        fundPoolAggrRoot.checkExist();

        SharingRuleAggrRoot sharingRuleAggrRoot = sharingRuleDomainService.querySharingRuleById(
                blackboard.getAggrRootIdNotNull(payloadVO.getSharingRuleIdInKey()));

        List<FundBillAggrRoot> fundBills = fundBillDomainRepository.batchQuery(
                FundBillAggrQuery.builder()
                        .type(FundBillTypeEnum.PAYMENT.getCode())
                        .orderSn(fundBillAggrRoot.getOrderSn())
                        .statusList(FundBillStatusEnum.IS_SHARED_LIST)
                        .build());
        if (sharingRuleAggrRoot.isAPISharing()) {
            genRefundSharingTransactionByAPi(fundBillAggrRoot, fundPoolAggrRoot, sharingRuleAggrRoot, fundBills, blackboard);
        } else if (sharingRuleAggrRoot.isRuleSharing()) {
            genRefundSharingTransactionByRule(fundBillAggrRoot, fundPoolAggrRoot, sharingRuleAggrRoot, fundBills, blackboard);
        }
        blackboard.setGlobal(payloadVO.getTransactionOutKey(), blackboard.getFundTransactionId());
        return Node.NodeStatus.SUCCESS;
    }

    @Override
    public Node.NodeStatus doConfirmStatus(ClearingCustomBlackboard blackboard, AbstractActionNode<?, ClearingCustomBlackboard> actionNode) {
        return blackboard.getNodeStatus(actionNode.getId());
    }

    public void genRefundSharingTransactionByAPi(FundBillAggrRoot bill, FundPoolAggrRoot pool
            , SharingRuleAggrRoot rule, List<FundBillAggrRoot> fundBills, ClearingCustomBlackboard blackboard) {
        Long totalShouldRefundAmount = bill.calculateSharingAmount(rule.getSharingBasis());
        Map<Long, SharingRuleAggrRoot> ruleMap = sharingRuleDomainService.getSharingRuleMapByBills(fundBills);

        for (FundBillAggrRoot fundBill : fundBills) {
            Long refundAmount = fundBill.updateRefundAmountAndGetRefundAmount(
                    totalShouldRefundAmount, ruleMap.get(fundBill.getSharingRuleId()).getSharingBasis());
            totalShouldRefundAmount -= refundAmount;
            if (totalShouldRefundAmount <= 0) {
                break;
            }
        }
        if (totalShouldRefundAmount > 0) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.REFUND_VALUE_GREATER_THAN_REFUNDABLE_AMOUNT);
        }
        List<ReceiverVO> receivers = bill.getReceivers()
                .stream()
                .map(receiver -> ReceiverVO.builder()
                        .id(receiver.getId())
                        .sharingAmount(receiver.getSharingAmount())
                        .sharingName(receiver.getSharingName())
                        .sharingScene(receiver.getSharingScene())
                        .build())
                .collect(Collectors.toList());
        updateAndPersist(bill, pool, rule, receivers, fundBills, blackboard);
    }

    public void genRefundSharingTransactionByRule(FundBillAggrRoot refundBill, FundPoolAggrRoot pool
            , SharingRuleAggrRoot rule, List<FundBillAggrRoot> payFundBills, ClearingCustomBlackboard blackboard) {
        Map<Long, SharingRuleAggrRoot> ruleMap = sharingRuleDomainService.getSharingRuleMapByBills(payFundBills);

        Long totalShouldRefundAmount = refundBill.calculateSharingAmount(rule.getSharingBasis());
        Map<Long, ReceiverVO> receiversMap = new HashMap<>();
        List<FundBillAggrRoot> fundBillAggrRoots = new ArrayList<>();
        for (FundBillAggrRoot payFundBill : payFundBills) {
            SharingRuleAggrRoot payBillSharingRule = ruleMap.get(payFundBill.getSharingRuleId());
            FundPoolSharingTransactionAggrRoot transaction = getTransactionByBill(payBillSharingRule, payFundBill);
            if (Objects.isNull(transaction)) {
                continue;
            }
            List<ReceiverVO> receiverValues = transaction.getReceivers();
            Long refundAmount = payFundBill.updateRefundAmountAndGetRefundAmount(totalShouldRefundAmount
                    , payBillSharingRule.getSharingBasis());
            fundBillAggrRoots.add(payFundBill);
            List<ReceiverVO> generatedReceivers = payFundBill.genRefundReceiver(receiverValues, refundAmount
                    , payBillSharingRule.getSharingBasis(), transaction.getSharingBaseAmount());

            // 将生成的接收方按ID累加到Map中
            for (ReceiverVO receiver : generatedReceivers) {
                Long receiverId = receiver.getId();
                if (receiversMap.containsKey(receiverId)) {
                    // 已存在该ID的接收方，累加金额
                    receiversMap.computeIfPresent(receiverId, (k, existingReceiver) -> ReceiverVO.builder()
                            .id(receiverId)
                            .sharingAmount(existingReceiver.getSharingAmount() + receiver.getSharingAmount())
                            .sharingName(receiver.getSharingName())
                            .sharingScene(receiver.getSharingScene())
                            .build());
                } else {
                    // 不存在该ID的接收方，直接添加
                    receiversMap.put(receiverId, receiver);
                }
            }
            totalShouldRefundAmount -= refundAmount;
        }
        if (totalShouldRefundAmount > 0) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.REFUND_VALUE_GREATER_THAN_REFUNDABLE_AMOUNT);
        }

        // 将Map转换为List
        List<ReceiverVO> receivers = new ArrayList<>(receiversMap.values());
        updateAndPersist(refundBill, pool, rule, receivers, fundBillAggrRoots, blackboard);
    }

    private void updateAndPersist(FundBillAggrRoot bill, FundPoolAggrRoot pool, SharingRuleAggrRoot rule
            , List<ReceiverVO> receivers, List<FundBillAggrRoot> fundBillAggrRoots, ClearingCustomBlackboard blackboard) {
        FundPoolSharingTransactionAggrRoot fundPoolSharingTransactionAggrRoot = genRefundTransaction(bill, pool, rule, receivers);
        List<FundPoolSharingTransBookAggrRoot> fundPoolSharingTransBookAggrRoots = generateSharingBooks(fundPoolSharingTransactionAggrRoot);
        FundPoolFlowAggrRoot fundPoolFlowAggrRoot = getFundPoolFlowAggrRoot(fundPoolSharingTransactionAggrRoot);
        bill.updateSharingRefundStatus(SharingActionStatusEnum.SUCCESS);
        fundBillAggrRoots.add(bill);
        blackboard.setFundTransactionId(fundPoolSharingTransactionAggrRoot.getIdStr());
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            fundPoolFlowDomainRepository.save(fundPoolFlowAggrRoot);
            fundBillDomainRepository.batchSave(fundBillAggrRoots);
            transactionDomainRepository.save(fundPoolSharingTransactionAggrRoot);
            fundPoolSharingTransBookDomainRepository.batchSave(fundPoolSharingTransBookAggrRoots);
        });
    }

    private FundPoolSharingTransactionAggrRoot getTransactionByBill(SharingRuleAggrRoot rule, FundBillAggrRoot fundBill) {
        List<FundPoolSharingTransactionAggrRoot> transactionAggrRoots = new ArrayList<>();
        if (rule.isOrderAmountSharingBase()) {
            transactionAggrRoots = transactionDomainRepository.batchQuery(
                    FundPoolSharingTransactionAggrQuery.builder()
                            .fundPoolId(fundBill.getFundPoolId())
                            .fundBillId(fundBill.getId())
                            .type(FundTransactionTypeEnum.PAYMENT.getCode())
                            .sharingType(SharingBaseEnum.ORDER_AMOUNT.getCode())
                            .statusList(FundPoolSharingTransactionStatusEnum.IS_SHARED_LIST)
                            .build());


        } else if (rule.isWalletAmountSharingBase()) {
            transactionAggrRoots = transactionDomainRepository.batchQuery(
                    FundPoolSharingTransactionAggrQuery.builder()
                            .fundPoolId(fundBill.getFundPoolId())
                            .type(FundTransactionTypeEnum.PAYMENT.getCode())
                            .sharingType(SharingBaseEnum.FUND_POOL_AMOUNT.getCode())
                            .statusList(FundPoolSharingTransactionStatusEnum.IS_SHARED_LIST)
                            .build());

        }
        if (CollectionUtils.isEmpty(transactionAggrRoots)) {
            return null;
        }
        return transactionAggrRoots.getFirst();
    }

    private FundPoolSharingTransactionAggrRoot genRefundTransaction(FundBillAggrRoot bill, FundPoolAggrRoot pool
            , SharingRuleAggrRoot rule, List<ReceiverVO> receivers) {
        return FundPoolSharingTransactionAggrRootFactory.builder()
                .coreBuilder()
                .id(DefaultSerialGenerator.getInstance().genFundPoolSharingTransactionId())
                .fundPoolId(bill.getFundPoolId())
                .fundBillId(bill.getId())
                .type(FundTransactionTypeEnum.REFUND)
                .sharingType(rule.getConfig().getSharingBase())
                .payeeType(bill.getPayeeType())
                .brandSn(bill.getBrandSn())
                .merchantSn(bill.getMerchantSn())
                .storeSn(bill.getStoreSn())
                .payeeInfo(bill.getPayeeInfo())
                .amount(FundPoolSharingTransactionAmountVO.builder()
                        .originAmount(bill.getOriginAmount())
                        .fee(bill.getFee())
                        .sharingBaseAmount(bill.calculateSharingAmount(rule.getSharingBasis()))
                        .sharingAmount(receivers.stream().map(ReceiverVO::getSharingAmount).reduce(0L, Long::sum))
                        .build())
                .bizDomain(FundPoolSharingTransactionBizDomainVO.builder()
                        .fundSource(BillSourceEnum.ofCode(pool.getBillSource().getCode()))
                        .fundSharingRuleId(rule.getId())
                        .receivers(receivers)
                        .build())
                .status(FundPoolSharingTransactionStatusEnum.PENDING_SETTLEMENT)
                .build();
    }

    private List<FundPoolSharingTransBookAggrRoot> generateSharingBooks(
            FundPoolSharingTransactionAggrRoot transaction) {
        // 批量查询所有需要的receiver
        List<Long> receiverIds = transaction.getReceivers()
                .stream()
                .map(ReceiverVO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(receiverIds)) {
            return List.of();
        }
        List<ReceiverAggrRoot> receiverAggrRoots = receiverDomainRepository.batchQuery(ReceiverAggrQuery.builder()
                .ids(receiverIds)
                .build());
        Map<Long, ReceiverAggrRoot> receiverCache = receiverAggrRoots.stream()
                .collect(Collectors.toMap(ReceiverAggrRoot::getId, Function.identity()));

        List<FundPoolSharingTransBookAggrRoot> sharingBooks = new ArrayList<>();
        for (ReceiverVO receiver : transaction.getReceivers()) {
            ReceiverAggrRoot receiverAggrRoot = receiverCache.get(receiver.getId());
            if (Objects.isNull(receiverAggrRoot)) {
                log.error("[分账接收方]>>>>>>接收方不存在, receiverId: {}", receiver.getId());
                throw new FundClearingBizException(FundClearingRespCodeEnum.RECEIVER_NOT_EXIST);
            }
            FundClearingBillAccountModel model = null;
            if (Objects.equals(receiverAggrRoot.getType(), AccountTypeEnum.BRAND)) {
                model = FundClearingBillAccountModel.builder()
                        .type(receiverAggrRoot.getType().getCode())
                        .brandSn(receiverAggrRoot.getSn())
                        .build();
            } else if (Objects.equals(receiverAggrRoot.getType(), AccountTypeEnum.MERCHANT)) {
                model = FundClearingBillAccountModel.builder()
                        .type(receiverAggrRoot.getType().getCode())
                        .merchantSn(receiverAggrRoot.getSn())
                        .build();
            } else if (Objects.equals(receiverAggrRoot.getType(), AccountTypeEnum.STORE)) {
                model = FundClearingBillAccountModel.builder()
                        .type(receiverAggrRoot.getType().getCode())
                        .storeSn(receiverAggrRoot.getSn())
                        .build();
            }

            ClearingAccountSupportService.AccountInfoResult accountInfoResult = clearingAccountSupportService.processAccountInfo(model);
            FundPoolSharingTransBookAggrRoot sharingBook = FundPoolSharingTransBookAggrRootFactory.builder()
                    .coreBuilder()
                    .id(DefaultSerialGenerator.getInstance().genFundPoolSharingTransBookId())
                    .transactionId(transaction.getId())
                    .type(transaction.getType())
                    .brandSn(transaction.getBrandSn())
                    .merchantSn(transaction.getMerchantSn())
                    .storeSn(transaction.getStoreSn())
                    .payeeType(transaction.getPayeeType())
                    .payeeSn(transaction.getPayeeSn())
                    .payeeInfo(transaction.getPayeeInfo())
                    .receiverType(receiverAggrRoot.getType())
                    .receiverSn(receiverAggrRoot.getSn())
                    .receiverInfo(accountInfoResult.buildPayeeInfoVO())
                    .amount(FundPoolSharingTransBookAmountVO.builder()
                            .originAmount(transaction.getAmount().getOriginAmount())
                            .fee(transaction.getAmount().getFee())
                            .sharingBaseAmount(transaction.getSharingBaseAmount())
                            .sharingAmount(receiver.getSharingAmount())
                            .build())
                    .status(FundPoolSharingTransBookStatusEnum.PENDING)
                    .build();
            sharingBooks.add(sharingBook);
        }
        return sharingBooks;
    }

    private static FundPoolFlowAggrRoot getFundPoolFlowAggrRoot(FundPoolSharingTransactionAggrRoot transaction) {
        return FundPoolFlowAggrRootFactory.builder()
                .coreBuilder()
                .id(DefaultSerialGenerator.getInstance().genFundPoolFlowId())
                .fundPoolId(transaction.getFundPoolId())
                .type(FundPoolFlowTypeEnum.CLEARING)
                .amount(FundPoolFlowAmountVO.builder()
                        .transAmount(transaction.getAmount().getSharingAmount())
                        .build())
                .businessType(FundPoolFlowBusinessTypeEnum.CLEARING)
                .businessSn(transaction.getIdStr())
                .status(FundPoolFlowStatusEnum.SUCCESS)
                .bizDomain(FundPoolFlowBizDomainVO.newEmptyInstance())
                .build();
    }

    @Getter
    @Builder
    @Jacksonized
    public static class GenRefundSharingTransactionActionFunctionPayload extends AbstractPayload {
        @FieldDescription("资金账单ID驶入键")
        @NotNull(message = "资金账单ID驶入键不能为空")
        private String billIdInKey;

        @FieldDescription("分账规则ID驶入键")
        @NotNull(message = "分账规则ID驶入键不能为空")
        private String sharingRuleIdInKey;

        @FieldDescription("分账交易ID驶出键")
        @NotNull(message = "分账交易ID驶出键不能为空")
        private String transactionOutKey;
    }

}