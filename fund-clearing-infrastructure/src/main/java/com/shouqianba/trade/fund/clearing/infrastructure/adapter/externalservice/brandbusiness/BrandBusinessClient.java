package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.common.template.InvokeTemplate;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req.BrandDetailInfoGetRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req.BrandMerchantInfoQueryRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandMerchantInfoQueryResult;
import com.wosai.cua.brand.business.api.dto.request.OutMerchantNoDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandDetailInfoDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandMerchantInfoDTO;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 品牌中心客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BrandBusinessClient {

    private static final int CACHE_MAX_SIZE = 1000;
    private static final int CACHE_EXPIRE_MINUTES = 30;
    private static final String CACHE_KEY_BRAND = "brand_";
    private static final String CACHE_KEY_MERCHANT_INNER = "merchant_inner_";
    private static final String CACHE_KEY_MERCHANT_OUTER = "merchant_outer_";

    private Cache<String, BrandDetailInfoGetResult> brandDetailCache;
    private Cache<String, BrandMerchantInfoQueryResult> merchantInfoCache;

    @Resource
    private BrandFacade brandFacade;

    @PostConstruct
    public void init() {
        this.brandDetailCache = buildCache();
        this.merchantInfoCache = buildCache();
    }

    private <T> Cache<String, T> buildCache() {
        return CacheBuilder
                .newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build();
    }

    public BrandDetailInfoGetResult getBrandDetailInfo(BrandDetailInfoGetRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected BrandDetailInfoGetResult invoke(BrandDetailInfoGetRequest request) {
                log.info("[品牌中心查询品牌信息]>>>>>>入参: {}", request.toJsonString());

                QueryBrandsDTO queryBrandsDto = request.genQueryBrandsDTO();
                if (Objects.isNull(queryBrandsDto)) {
                    return BrandDetailInfoGetResult.DEFAULT;
                }

                String cacheKey = CACHE_KEY_BRAND + queryBrandsDto.getBrandSn() + "_" + queryBrandsDto.getBrandId();

                BrandDetailInfoGetResult cacheResult = brandDetailCache.getIfPresent(cacheKey);
                if (cacheResult != null) {
                    return cacheResult;
                }

                log.info("[品牌中心查询品牌信息]>>>>>>缓存未命中，从远程获取");
                BrandDetailInfoDTO brandDetailInfo = brandFacade.getBrandDetailInfoByBrandSn(queryBrandsDto);

                BrandDetailInfoGetResult result = brandDetailInfo != null ?
                        BrandDetailInfoGetResult.from(brandDetailInfo) : BrandDetailInfoGetResult.DEFAULT;

                brandDetailCache.put(cacheKey, result);
                log.info("[品牌中心查询品牌信息]>>>>>>出参: {}", JsonUtils.toJsonString(result));
                return result;

            }

            @Override
            protected BrandDetailInfoGetResult onFailure(BrandDetailInfoGetRequest request, Throwable throwable) {
                log.warn("[品牌中心查询品牌信息]>>>>>>异常栈:", throwable);
                String cacheKey = CACHE_KEY_BRAND + request.getBrandSn();
                return cacheFailureResult(brandDetailCache, cacheKey, BrandDetailInfoGetResult.DEFAULT);
            }
        });
    }

    public BrandMerchantInfoQueryResult queryBrandMerchantInfo(BrandMerchantInfoQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected BrandMerchantInfoQueryResult invoke(BrandMerchantInfoQueryRequest request) {
                log.info("[品牌中心查询商户信息]>>>>>>入参: {}", request.toJsonString());

                Object queryDTO = request.genQueryDTO();
                if (Objects.isNull(queryDTO)) {
                    return BrandMerchantInfoQueryResult.DEFAULT;
                }

                String cacheKey = generateMerchantCacheKey(request, queryDTO);
                return getMerchantInfoWithCache(cacheKey, request, queryDTO);
            }

            @Override
            protected BrandMerchantInfoQueryResult onFailure(
                    BrandMerchantInfoQueryRequest request, Throwable throwable) {
                log.warn("[品牌中心查询商户信息]>>>>>>异常栈:", throwable);
                Object queryDTO = request.genQueryDTO();
                String cacheKey = generateMerchantCacheKey(request, queryDTO);
                return cacheFailureResult(merchantInfoCache, cacheKey, BrandMerchantInfoQueryResult.DEFAULT);
            }
        });
    }

    private BrandMerchantInfoQueryResult getMerchantInfoWithCache(
            String cacheKey,
            BrandMerchantInfoQueryRequest request, Object queryDTO) {
        BrandMerchantInfoQueryResult cacheResult = merchantInfoCache.getIfPresent(cacheKey);
        if (cacheResult != null) {
            return cacheResult;
        }

        log.info("[品牌中心查询商户信息]>>>>>>缓存未命中，从远程获取");
        BrandMerchantInfoDTO merchantInfo = request.isInnerMerchantQuery() ?
                brandFacade.getBrandMerchantInfoByStoreIdOrMerchantSn((QueryBrandMerchantInfoDTO) queryDTO) :
                brandFacade.getBrandMerchantInfoByOutMerchantNo((OutMerchantNoDTO) queryDTO);

        BrandMerchantInfoQueryResult result = merchantInfo != null ?
                BrandMerchantInfoQueryResult.from(merchantInfo) : BrandMerchantInfoQueryResult.DEFAULT;

        merchantInfoCache.put(cacheKey, result);
        log.info("[品牌中心查询商户信息]>>>>>>出参: {}", JsonUtils.toJsonString(result));
        return result;
    }

    private String generateMerchantCacheKey(BrandMerchantInfoQueryRequest request, Object queryDTO) {
        return request.isInnerMerchantQuery() ?
                CACHE_KEY_MERCHANT_INNER + JsonUtils.toJsonString(queryDTO) :
                CACHE_KEY_MERCHANT_OUTER + JsonUtils.toJsonString(queryDTO);
    }

    private <T> T cacheFailureResult(Cache<String, T> cache, String cacheKey, T defaultValue) {
        cache.put(cacheKey, defaultValue);
        return defaultValue;
    }

}
