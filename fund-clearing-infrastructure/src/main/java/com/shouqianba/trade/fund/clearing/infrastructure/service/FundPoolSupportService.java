package com.shouqianba.trade.fund.clearing.infrastructure.service;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.common.template.InvokeTemplate;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query.FundPoolAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.service.model.req.QueryFundPoolRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资金池支持服务
 *
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Slf4j
@Component
public class FundPoolSupportService {

    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;

    /**
     * 查询资金池
     *
     * @param request 查询资金池请求
     * @return 查询结果状态
     */
    public FundPoolAggrRoot queryFundPool(QueryFundPoolRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundPoolAggrRoot invoke(QueryFundPoolRequest request) {
                request.checkParams();
                log.info(
                        "[查询资金池]入参：payeeType：{}, brandSn：{}, merchantSn：{}, storeSn：{}, billSource：{}, settleDate：{}",
                        request.getPayeeType(), request.getBrandSn(), request.getMerchantSn(), request.getStoreSn(),
                        request.getBillSource(), request.getSettleDate());

                FundPoolAggrQuery.FundPoolAggrQueryBuilder queryBuilder = FundPoolAggrQuery
                        .builder()
                        .payeeType(request.getPayeeType())
                        .acquiringCompany(request.getAcquiringCompany())
                        .billSource(request.getBillSource())
                        .settleDate(request.getSettleDate());

                // 根据PayeeType选择对应的SN进行查询
                switch (request.getPayeeType()) {
                    case BRAND:
                        queryBuilder.brandSn(request.getBrandSn());
                        break;
                    case MERCHANT:
                        queryBuilder.merchantSn(request.getMerchantSn());
                        break;
                    case STORE:
                        queryBuilder.storeSn(request.getStoreSn());
                        break;
                    default:
                        log.warn("[查询资金池]未知的收款方类型: {}", request.getPayeeType());
                        throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT);
                }

                List<FundPoolAggrRoot> fundPoolAggrRoots = fundPoolDomainRepository.batchQuery(queryBuilder.build());
                if (CollectionUtils.isEmpty(fundPoolAggrRoots)) {
                    log.info("[查询资金池]未查询到资金池");
                    return FundPoolAggrRoot.newEmptyInstance();
                }

                FundPoolAggrRoot fundPoolAggrRoot = fundPoolAggrRoots.getFirst();
                log.info("[查询资金池]查询到资金池: {}", fundPoolAggrRoot.getId());
                return fundPoolAggrRoot;
            }

            @Override
            protected FundPoolAggrRoot onFailure(QueryFundPoolRequest request, Throwable throwable) {
                log.error("[查询资金池]异常栈>>>", throwable);
                throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_POOL_NOT_EXIST);
            }
        });
    }

    public FundPoolAggrRoot queryFundPoolById(Long id) {
        return fundPoolDomainRepository.query(FundPoolAggrQuery
                .builder()
                .id(id)
                .build());
    }

    public FundPoolAggrRoot queryFundPoolNotNullById(Long id) {
        FundPoolAggrRoot fundPoolAggrRoot = queryFundPoolById(id);
        fundPoolAggrRoot.checkExist();
        return fundPoolAggrRoot;
    }

    /**
     * 根据资金账单查询资金池
     *
     * @param fundBillAggrRoot 资金账单聚合根
     * @return 资金池聚合根
     */
    public FundPoolAggrRoot queryFundPoolByBill(FundBillAggrRoot fundBillAggrRoot) {
        return queryFundPool(QueryFundPoolRequest
                .builder()
                    .payeeType(fundBillAggrRoot.getPayeeType())
                    .brandSn(fundBillAggrRoot.getBrandSn())
                    .merchantSn(fundBillAggrRoot.getMerchantSn())
                    .storeSn(fundBillAggrRoot.getStoreSn())
                    .billSource(fundBillAggrRoot.getBillSource())
                    .settleDate(fundBillAggrRoot.getSettleDate())
                .build());
    }

}
