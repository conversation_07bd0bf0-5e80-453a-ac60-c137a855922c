package com.shouqianba.trade.fund.clearing.infrastructure.repository.domain.converter;

import com.google.common.collect.Lists;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolExtVO;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.po.FundPoolPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Component
public class FundPoolAggrRootConverter {

    public FundPoolPO toFundPoolPO(FundPoolAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_POOL_NOT_EXIST);
        }
        return new FundPoolPO()
                .setId(aggrRoot.getId())
                .setBatchId(aggrRoot.getBatchId())
                .setPayeeType(aggrRoot
                        .getPayeeType()
                        .getCode())
                .setBrandSn(aggrRoot.getBrandSn())
                .setMerchantSn(aggrRoot.getMerchantSn())
                .setStoreSn(aggrRoot.getStoreSn())
                .setPayeeInfo(aggrRoot
                        .getPayeeInfo()
                        .toJsonNode())
                .setAcquiringCompany(aggrRoot.getAcquiringCompany())
                .setBillSource(Objects.nonNull(aggrRoot.getBillSource()) ? aggrRoot
                        .getBillSource()
                        .getCode() : null)
                .setSettleDate(aggrRoot.getSettleDate())
                .setAmount(aggrRoot
                        .getAmount()
                        .toJsonNode())
                .setBizDomain(aggrRoot
                        .getBizDomain()
                        .toJsonNode())
                .setStatus(aggrRoot
                        .getStatus()
                        .getCode())
                .setExt(aggrRoot
                        .getExt()
                        .toJsonNode())
                .setCtime(aggrRoot.getCreated())
                .setMtime(aggrRoot.getUpdated())
                .setVersion(aggrRoot.getVersion());
    }

    public FundPoolAggrRoot toFundPoolAggrRoot(FundPoolPO po) {
        if (Objects.isNull(po)) {
            return FundPoolAggrRoot.newEmptyInstance();
        }
        try {
            return FundPoolAggrRootFactory
                    .builder()
                    .coreBuilder()
                    .id(po.getId())
                    .batchId(po.getBatchId())
                    .payeeType(AccountTypeEnum.ofCode(po.getPayeeType()))
                    .brandSn(po.getBrandSn())
                    .merchantSn(po.getMerchantSn())
                    .storeSn(po.getStoreSn())
                    .payeeInfo(PayeeInfoVO.genFromJsonObject(po.getPayeeInfo(), PayeeInfoVO.class))
                    .acquiringCompany(po.getAcquiringCompany())
                    .billSource(BillSourceEnum.ofCode(po.getBillSource()))
                    .settleDate(po.getSettleDate())
                    .amount(FundPoolAmountVO.genFromJsonObject(po.getAmount(), FundPoolAmountVO.class))
                    .bizDomain(FundPoolBizDomainVO.genFromJsonObject(po.getBizDomain(), FundPoolBizDomainVO.class))
                    .status(FundPoolStatusEnum.ofCode(po.getStatus()))
                    .optionalBuilder()
                    .ext(FundPoolExtVO.genFromJsonObject(po.getExt(), FundPoolExtVO.class))
                    .created(po.getCtime())
                    .updated(po.getMtime())
                    .version(po.getVersion())
                    .rebuild();
        } catch (Exception e) {
            return FundPoolAggrRoot.newEmptyInstance();
        }
    }

    public List<FundPoolAggrRoot> toFundPoolAggrRootList(List<FundPoolPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Lists.newArrayListWithCapacity(0);
        }
        List<FundPoolAggrRoot> aggrRoots = Lists.newArrayListWithCapacity(poList.size());
        FundPoolAggrRoot aggrRoot;
        for (FundPoolPO po : poList) {
            aggrRoot = toFundPoolAggrRoot(po);
            if (aggrRoot.isExist()) {
                aggrRoots.add(aggrRoot);
            }
        }
        return aggrRoots;
    }
}
