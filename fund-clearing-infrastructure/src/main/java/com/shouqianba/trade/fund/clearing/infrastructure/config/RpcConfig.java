package com.shouqianba.trade.fund.clearing.infrastructure.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.trade.fund.core.api.ChannelConfigService;
import com.shouqianba.trade.fund.settlement.api.SettlementBatchService;
import com.shouqianba.trade.fund.settlement.api.SettlementFlowService;
import com.shouqianba.trade.fund.settlement.api.WithdrawService;
import com.wosai.authorize.service.DouYinAuthorizeService;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.trade.service.TradeStateService;
import com.wosai.upay.core.service.StoreService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR> Date: 2023/6/21 Time: 10:57
 */
@Configuration
public class RpcConfig {

    @Bean
    public JsonProxyFactoryBean brandFacade(@Value("${service.rpc.brand-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/brand");
        jsonProxyFactoryBean.setServiceInterface(BrandFacade.class);
        jsonProxyFactoryBean.setServerName("brand-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean tradeStateService(@Value("${service.rpc.trade-manage}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/trade_state");
        jsonProxyFactoryBean.setServiceInterface(TradeStateService.class);
        jsonProxyFactoryBean.setServerName("trade-manage-service");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean channelConfigService(@Value("${service.rpc.fund-core}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/channel");
        jsonProxyFactoryBean.setServiceInterface(ChannelConfigService.class);
        jsonProxyFactoryBean.setServerName("fund-core");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(10000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean settlementFlowService(@Value("${service.rpc.fund-settlement}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/settlementFlow");
        jsonProxyFactoryBean.setServiceInterface(SettlementFlowService.class);
        jsonProxyFactoryBean.setServerName("fund-settlement");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(10000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        jsonProxyFactoryBean.setExtraHttpHeaders(Map.of("x-env-flag", "trade2069"));

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean withdrawService(@Value("${service.rpc.fund-settlement}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/withdraw");
        jsonProxyFactoryBean.setServiceInterface(WithdrawService.class);
        jsonProxyFactoryBean.setServerName("fund-settlement");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(10000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        jsonProxyFactoryBean.setExtraHttpHeaders(Map.of("x-env-flag", "trade2135"));

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean settlementBatchService(@Value("${service.rpc.fund-settlement}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/settlementBatch");
        jsonProxyFactoryBean.setServiceInterface(SettlementBatchService.class);
        jsonProxyFactoryBean.setServerName("fund-settlement");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(10000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);
        jsonProxyFactoryBean.setExtraHttpHeaders(Map.of("x-env-flag", "trade2135"));

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean douYinAuthorizeService(@Value("${service.rpc.scorpio}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/douyin");
        jsonProxyFactoryBean.setServiceInterface(DouYinAuthorizeService.class);
        jsonProxyFactoryBean.setServerName("scorpio");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean storeService(@Value("${service.rpc.core-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/store");
        jsonProxyFactoryBean.setServiceInterface(StoreService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }


}
