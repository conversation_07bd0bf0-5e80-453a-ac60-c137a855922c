package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res;

import com.shouqianba.trade.fund.settlement.api.result.WithdrawCreateResult;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/5/20 Time: 04:20 PM
 */
@Builder
@Getter
public class FundClearingWithdrawResult {
    public static final FundClearingWithdrawResult DEFAULT = FundClearingWithdrawResult
            .builder().build();

    private String withdrawSn;

    public static FundClearingWithdrawResult newInstance(WithdrawCreateResult result) {
        return FundClearingWithdrawResult
                .builder()
                .withdrawSn(result.getWithdrawSn())
                .build();
    }

    public boolean isExist() {
        return Objects.nonNull(withdrawSn);
    }
}
