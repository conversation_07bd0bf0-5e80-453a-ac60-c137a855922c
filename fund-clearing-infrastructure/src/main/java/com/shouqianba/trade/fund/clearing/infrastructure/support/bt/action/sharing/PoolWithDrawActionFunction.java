package com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.sharing;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query.FundPoolAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingWithdrawRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.annotation.FieldDescription;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.AbstractClearingFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.util.SpringContextUtils;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.AbstractPayload;
import com.wosai.general.ds.bt.Node;
import com.wosai.general.ds.bt.leaf.AbstractActionNode;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR> Date: 2025/4/25 Time: 17:31
 */
@Slf4j
@JsonTypeName(PoolWithDrawActionFunction.TYPE)
public class PoolWithDrawActionFunction extends AbstractClearingFunction {
    protected static final String TYPE = "PoolWithDrawActionFunction";
    @JsonIgnore
    protected final transient TransactionSharingSettleActionFunctionPayload payloadVO
            = AbstractPayload.convertPayload(getPayload(), TransactionSharingSettleActionFunctionPayload.class);

    @JsonCreator
    public PoolWithDrawActionFunction(
            @JsonProperty("payload") Map<String, Object> payload) {
        super(TYPE, payload);
    }

    @Override
    public Node.NodeStatus doApply(ClearingCustomBlackboard blackboard, AbstractActionNode<?
            , ClearingCustomBlackboard> actionNode) {
        FundPoolDomainRepository fundPoolDomainRepository = SpringContextUtils.getBean(FundPoolDomainRepository.class);
        SettleClient settleClient = SpringContextUtils.getBean(SettleClient.class);
        FundPoolAggrRoot fundPool = fundPoolDomainRepository.query(
                FundPoolAggrQuery.builder()
                        .id(blackboard.getAggrRootIdNotNull(payloadVO.getPoolIdInKey()))
                        .status(FundPoolStatusEnum.SETTLEMENT_COMPLETED)
                        .build());
        settleClient.createWithdraw(FundClearingWithdrawRequest.builder()
                .clientSn(fundPool.getIdStr())
                .withdrawAllAmount(Boolean.TRUE)
                .merchantId(fundPool.getPayeeInfo().getFundMerchantId())
                .brandSn(fundPool.getPayeeInfo().getBrandSn())
                .merchantSn(fundPool.getPayeeInfo().getFundMerchantSn())
                .acquiringCompany(fundPool.getAcquiringCompany())
                .statisticsDate(fundPool.getSettleDate().format(FundPoolAggrRoot.FORMATTER))
                .build());
        return Node.NodeStatus.SUCCESS;
    }


    @Override
    public Node.NodeStatus doConfirmStatus(ClearingCustomBlackboard blackboard, AbstractActionNode<?, ClearingCustomBlackboard> actionNode) {
        return blackboard.getNodeStatus(actionNode.getId());
    }


    @Getter
    @Builder
    @Jacksonized
    public static class TransactionSharingSettleActionFunctionPayload extends AbstractPayload {
        @FieldDescription("资金池ID驶入键")
        @NotNull(message = "资金池ID驶入键不能为空")
        private String poolIdInKey;
    }

}