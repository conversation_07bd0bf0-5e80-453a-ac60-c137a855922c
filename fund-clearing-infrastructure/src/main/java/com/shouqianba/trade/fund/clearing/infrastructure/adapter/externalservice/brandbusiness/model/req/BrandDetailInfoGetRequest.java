package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req;

import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/11 Time: 10:20 AM
 */

@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class BrandDetailInfoGetRequest extends BaseRequest {
    private String brandId;
    private String brandSn;

    public QueryBrandsDTO genQueryBrandsDTO() {
        if (Objects.isNull(this.brandId) && Objects.isNull(this.brandSn)) {
            return null;
        }

        QueryBrandsDTO queryBrandsDto = new QueryBrandsDTO();
        queryBrandsDto.setBrandSn(this.brandSn);
        queryBrandsDto.setBrandId(this.brandId);

        return queryBrandsDto;
    }
}
