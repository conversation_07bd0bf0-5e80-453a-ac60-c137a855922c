package com.shouqianba.trade.fund.clearing.infrastructure.repository.domain;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query.FundPoolAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbatch.model.FundBatchQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.FundPoolDao;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.model.FundPoolQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.repository.domain.converter.FundPoolAggrRootConverter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Repository
public class FundPoolDomainRepositoryImpl implements FundPoolDomainRepository {

    @Resource
    private FundPoolDao fundPoolDao;
    @Resource
    private FundPoolAggrRootConverter converter;

    @Override
    public void save(FundPoolAggrRoot aggrRoot) {
            if (aggrRoot.isNeedAdd()) {
            fundPoolDao.insert(converter.toFundPoolPO(aggrRoot));
        } else if (aggrRoot.isNeedModify()) {
            int affectRow = fundPoolDao.update(converter.toFundPoolPO(aggrRoot));
            if (affectRow <= 0) {
                throw new FundClearingBizException(FundClearingRespCodeEnum.CONCURRENT_MODIFY_ERROR);
            }
        }
    }

    @Override
    public void batchSave(List<FundPoolAggrRoot> aggrRoots) {
        if (Objects.isNull(aggrRoots) || aggrRoots.isEmpty()) {
            return;
        }

        List<FundPoolAggrRoot> needAddList = aggrRoots.stream()
                .filter(FundPoolAggrRoot::isNeedAdd)
                .collect(Collectors.toList());
        if (!needAddList.isEmpty()) {
            fundPoolDao.batchInsert(needAddList.stream()
                    .map(converter::toFundPoolPO)
                    .collect(Collectors.toList()));
        }

        for (FundPoolAggrRoot aggrRoot : aggrRoots) {
            if (aggrRoot.isNeedModify()) {
                int affectRow = fundPoolDao.update(converter.toFundPoolPO(aggrRoot));
                if (affectRow <= 0) {
                    throw new FundClearingBizException(FundClearingRespCodeEnum.CONCURRENT_MODIFY_ERROR);
                }
            }
        }
    }

    @Override
    public FundPoolAggrRoot query(FundPoolAggrQuery aggrQuery) {
        return converter.toFundPoolAggrRoot(fundPoolDao.select(FundPoolQuery.genFundPoolQuery(aggrQuery)));
    }


    @Override
    public List<FundPoolAggrRoot> batchQuery(FundPoolAggrQuery aggrQuery) {
        return converter.toFundPoolAggrRootList(
                fundPoolDao.batchSelect(FundPoolQuery.genFundPoolQuery(aggrQuery)));
    }

    @Override
    public Long count(FundPoolAggrQuery query) {
        return fundPoolDao.count(FundPoolQuery.genFundPoolQuery(query));
    }
}
