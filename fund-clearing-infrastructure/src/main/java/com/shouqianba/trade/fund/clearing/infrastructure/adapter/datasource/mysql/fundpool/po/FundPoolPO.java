package com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.po;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Data
@Accessors(chain = true)
public class FundPoolPO {

    private Long id;
    private Long batchId;
    private Byte payeeType;
    private String brandSn;
    private String merchantSn;
    private String storeSn;
    private JsonNode payeeInfo;
    private Integer acquiringCompany;
    private Byte billSource;
    private LocalDate settleDate;
    private JsonNode amount;
    private JsonNode bizDomain;
    private Byte status;
    private JsonNode ext;
    private LocalDateTime ctime;
    private LocalDateTime mtime;
    private Long version;

}
