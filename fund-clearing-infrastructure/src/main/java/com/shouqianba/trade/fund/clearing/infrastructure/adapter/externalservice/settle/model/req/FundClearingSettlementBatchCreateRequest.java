package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req;

import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.BaseRequest;
import com.shouqianba.trade.fund.settlement.api.request.SettlementBatchCreateRequest;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAccountModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAmountModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementBatchBizInfoModel;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2025/5/20 Time: 04:20 PM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class FundClearingSettlementBatchCreateRequest extends BaseRequest {

    private Byte type;  // 批次类型:1-入账结算 2-分账结算
    private AccountInfo fromInfo;  // 付款方信息
    private AccountInfo toInfo;  // 收款方信息
    private AmountInfo amount;  // 金额信息
    private Byte settleType;  // 结算类型：1-结算、2-收款、3-退款、4-分账、5-分账回退
    private Integer acquiringCompany;  // 收单机构

    @Getter
    @Builder(toBuilder = true)
    public static class AccountInfo {
        private Byte type;
        private String brandSn;
        private String merchantSn;
        private String storeSn;
        private String channelMerchantSn;

        public FundSettlementAccountModel toModel() {
            return FundSettlementAccountModel
                    .builder()
                    .type(type)
                    .brandSn(brandSn)
                    .merchantSn(merchantSn)
                    .storeSn(storeSn)
                    .channelMerchantSn(channelMerchantSn)
                    .build();
        }
    }

    @Getter
    @Builder(toBuilder = true)
    public static class AmountInfo {
        private Long originAmount;
        private Long fee;
        private Long settleAmount;

        public FundSettlementAmountModel toModel() {
            return FundSettlementAmountModel
                    .builder()
                    .originAmount(originAmount)
                    .fee(fee)
                    .settleAmount(settleAmount)
                    .build();
        }
    }

    public SettlementBatchCreateRequest genSettlementBatchCreateRequest() {
        FundSettlementBatchBizInfoModel bizInfo = FundSettlementBatchBizInfoModel
                .builder()
                .settleType(settleType)
                .acquiringCompany(acquiringCompany)
                .build();

        return SettlementBatchCreateRequest
                .builder()
                .type(type)
                .fromInfo(fromInfo.toModel())
                .toInfo(toInfo.toModel())
                .amount(amount.toModel())
                .bizInfo(bizInfo)
                .build();
    }
}
