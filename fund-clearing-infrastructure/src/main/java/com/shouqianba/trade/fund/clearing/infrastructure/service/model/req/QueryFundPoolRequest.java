package com.shouqianba.trade.fund.clearing.infrastructure.service.model.req;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 查询资金池请求
 *
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryFundPoolRequest {

    /**
     * 收款方类型
     */
    private AccountTypeEnum payeeType;

    /**
     * 品牌SN
     */
    private String brandSn;

    /**
     * 商户SN
     */
    private String merchantSn;

    /**
     * 门店SN
     */
    private String storeSn;

    /**
     * 收单机构
     */
    private Integer acquiringCompany;

    /**
     * 账单来源
     */
    private BillSourceEnum billSource;

    /**
     * 结算时间
     */
    private LocalDate settleDate;

    /**
     * 参数校验
     */
    public void checkParams() {
        if (Objects.isNull(payeeType) || Objects.isNull(billSource) || Objects.isNull(settleDate) ) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT);
        }

        // 根据收款方类型校验对应的SN
        switch (payeeType) {
            case BRAND:
                if (Objects.isNull(brandSn) || brandSn.isEmpty()) {
                    throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT);
                }
                break;
            case MERCHANT:
                if (Objects.isNull(merchantSn) || merchantSn.isEmpty()) {
                    throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT);
                }
                break;
            case STORE:
                if (Objects.isNull(storeSn) || storeSn.isEmpty()) {
                    throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT);
                }
                break;
            default:
                throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT);
        }
    }
}