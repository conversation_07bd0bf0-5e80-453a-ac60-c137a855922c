package com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbill.model;

import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.BaseQuery;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Setter
@SuperBuilder(toBuilder = true)
public class FundBillQuery extends BaseQuery {
    private Long id;
    private List<Long> ids;
    private Long fundBatchId;
    private Long fundPoolId;
    private Long fundPoolIds;
    private LocalDate acquiringDate;
    private LocalDate billDate;
    private Byte payeeType;
    private String brandSn;
    private String merchantSn;
    private String storeSn;
    private Byte billSource;
    private Byte type;
    private Byte status;
    private List<Byte> statusList;
    private String transSn;
    private String orderSn;

    public static FundBillQuery genFundBillQuery(FundBillAggrQuery aggrQuery) {
        if (aggrQuery.isSingleIdQuery()) {
            return FundBillQuery
                    .builder()
                    .id(aggrQuery.getId())
                    .status(aggrQuery.getStatus())
                    .build();
        }

        return FundBillQuery
                .builder()
                .ids(aggrQuery.getIds())
                .fundBatchId(aggrQuery.getFundBatchId())
                .fundPoolId(aggrQuery.getPoolId())
                .acquiringDate(aggrQuery.getAcquiringDate())
                .billDate(aggrQuery.getBillDate())
                .payeeType(aggrQuery.getPayeeType())
                .brandSn(aggrQuery.getBrandSn())
                .merchantSn(aggrQuery.getMerchantSn())
                .storeSn(aggrQuery.getStoreSn())
                .billSource(aggrQuery.getBillSource())
                .type(aggrQuery.getType())
                .status(aggrQuery.getStatus())
                .statusList(aggrQuery.getStatusList())
                .transSn(aggrQuery.getTransSn())
                .orderSn(aggrQuery.getOrderSn())
                .sortField(aggrQuery.getSortField())
                .endCursor(aggrQuery.getEndCursor())
                .cursorField(aggrQuery.getCursorField())
                .isDesc(aggrQuery.isDesc())
                .querySize(aggrQuery.getQuerySize())
                .build();
    }
}
