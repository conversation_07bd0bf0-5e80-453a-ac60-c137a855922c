package com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.entry;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.model.ConfigFundCollectRuleConfigVO;
import com.shouqianba.trade.fund.clearing.infrastructure.annotation.FieldDescription;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.AbstractPayload;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.resp.ConfigFundCollectRuleConfigResultModel;
import com.wosai.general.ds.bt.Node;
import com.wosai.general.ds.bt.leaf.AbstractActionNode;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 创建资金池功能
 * <p>
 * 检查资金池和批次是否存在，不存在则创建新的
 *
 * <AUTHOR> Date: 2025/06/11 Time: 10:30AM
 */
@Slf4j
@JsonTypeName(CreateFundPoolAndBatchIfNotExistsFunction.TYPE)
public class CreateFundPoolAndBatchIfNotExistsFunction extends AbstractEntryFunction {
    protected static final String TYPE = "create_fund_pool_if_not_exists_function";
    @JsonIgnore
    protected final transient CreateFundPoolIfNotExistsFunctionPayload payloadVO =
            AbstractPayload.convertPayload(getPayload(), CreateFundPoolIfNotExistsFunctionPayload.class);

    @JsonCreator
    public CreateFundPoolAndBatchIfNotExistsFunction(
            @JsonProperty("payload") Map<String, Object> payload) {
        super(TYPE, payload);
    }

    @Override
    public Node.NodeStatus doApply(
            ClearingCustomBlackboard blackboard,
            AbstractActionNode<?, ClearingCustomBlackboard> actionNode) {

        FundBillAggrRoot fundBillAggrRoot =
                getFundBillAggrRootNotNull(blackboard.getAggrRootIdNotNull(payloadVO.getBillIdInKey()));
        fundBillAggrRoot.checkExist();

        ConfigFundCollectRuleConfigResultModel configModel = getConfigFundCollectRuleConfigResultModelNotNull(
                blackboard.getAggrRootIdNotNull(payloadVO.getCollectRuleBelongIdInKey()));

        List<FundBatchAggrRoot> fundBatchAggrRoots = fundBatchDomainRepository.batchQuery(FundBatchAggrQuery
                .builder()
                .brandSn(fundBillAggrRoot.getBrandSn())
                .billSource(fundBillAggrRoot
                        .getBillSource()
                        .getCode())
                .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                .settleTime(fundBillAggrRoot.getSettleDate())
                .querySize(1)
                .build());

        if (CollectionUtils.isEmpty(fundBatchAggrRoots)) {
            try {
                fundBatchDomainRepository.save(FundBatchAggrRootFactory
                        .builder()
                        .coreBuilder()
                        .id(DefaultSerialGenerator
                                .getInstance()
                                .genFundBatchId())
                        .payeeType(fundBillAggrRoot.getPayeeType())
                        .brandSn(fundBillAggrRoot.getBrandSn())
                        .merchantSn(fundBillAggrRoot.getMerchantSn())
                        .storeSn(fundBillAggrRoot.getStoreSn())
                        .payeeInfo(fundBillAggrRoot.getPayeeInfo())
                        .billSource(fundBillAggrRoot.getBillSource())
                        .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                        .settleTime(fundBillAggrRoot.getSettleDate())
                        .status(FundBatchStatusEnum.ENTERING)
                        .amount(FundBatchAmountVO.newEmptyInstance())
                        .build());
            } catch (DuplicateKeyException ignore) {
            }
            fundBatchAggrRoots = fundBatchDomainRepository.batchQuery(FundBatchAggrQuery
                    .builder()
                    .brandSn(fundBillAggrRoot.getBrandSn())
                    .billSource(fundBillAggrRoot
                            .getBillSource()
                            .getCode())
                    .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                    .settleTime(fundBillAggrRoot.getSettleDate())
                    .querySize(1)
                    .build());
            if (CollectionUtils.isEmpty(fundBatchAggrRoots)) {
                return Node.NodeStatus.FAILURE;
            }
        }
        // 查询资金池
        FundPoolAggrRoot fundPoolAggrRoot = fundPoolSupportService.queryFundPoolByBill(fundBillAggrRoot);
        if (fundPoolAggrRoot.isExist()) {

            if (fundPoolAggrRoot.isEntrySettled()) {
                log.info("[执行资金池入账功能]已进入结算极端，无法再入账，请检查，fundBillId=[{}], fundPoolId=[{}]",
                        fundBillAggrRoot.getId(), fundPoolAggrRoot.getId());
                return Node.NodeStatus.FAILURE;
            }

            blackboard.setGlobal(payloadVO.getPoolIdOutKey(), fundPoolAggrRoot.getIdStr());
            return Node.NodeStatus.SUCCESS;
        }

        // 创建资金池
        ConfigFundCollectRuleConfigVO config = configModel.getConfig();
        FundPoolAggrRoot fundPoolAggrRootCreate = FundPoolAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genFundPoolId())
                .batchId(fundBatchAggrRoots
                        .getFirst()
                        .getId())
                .payeeType(fundBillAggrRoot.getPayeeType())
                .brandSn(fundBillAggrRoot.getBrandSn())
                .merchantSn(fundBillAggrRoot.getMerchantSn())
                .storeSn(fundBillAggrRoot.getStoreSn())
                .payeeInfo(fundBillAggrRoot.getPayeeInfo())
                .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                .billSource(fundBillAggrRoot.getBillSource())
                .settleDate(fundBillAggrRoot.getSettleDate())
                .amount(FundPoolAmountVO.newEmptyInstance())
                .bizDomain(FundPoolBizDomainVO
                        .builder()
                        .billEntrySettleBehaviorTreeId(config.getEntrySettleBehaviorTreeId())
                        .entryActionStatus(FundPoolBizDomainVO.EntryActionStatusVO.newEmptyInstance())
                        .sharingActionStatus(FundPoolBizDomainVO.SharingActionStatusVO
                                .builder()
                                .build())
                        .fundCollectRuleBelongId(fundBillAggrRoot
                                .getBizDomain()
                                .getFundCollectRuleBelongId())
                        .billEntrySettleBehaviorTreeId(config.getEntrySettleBehaviorTreeId())
                        .entryActionStatus(FundPoolBizDomainVO.EntryActionStatusVO.newEmptyInstance())
                        .payway(fundBillAggrRoot.getPayway())
                        .build())
                .status(FundPoolStatusEnum.ENTRY_IN_PROGRESS)
                .build();
        try {
            fundPoolDomainRepository.save(fundPoolAggrRootCreate);
            blackboard.setGlobal(payloadVO.getPoolIdOutKey(), fundPoolAggrRootCreate.getIdStr());
        } catch (DuplicateKeyException e) {
            FundPoolAggrRoot fundPoolAggrRootCurrent =
                    computeFundPoolAggrRootByBill(blackboard.getAggrRootIdNotNull(payloadVO.getBillIdInKey()));
            fundPoolAggrRootCurrent.checkExist();
            blackboard.setGlobal(payloadVO.getPoolIdOutKey(), fundPoolAggrRootCurrent.getIdStr());
        }

        return Node.NodeStatus.SUCCESS;
    }

    @Override
    public Node.NodeStatus doConfirmStatus(
            ClearingCustomBlackboard blackboard,
            AbstractActionNode<?, ClearingCustomBlackboard> actionNode) {
        FundPoolAggrRoot fundPoolAggrRoot =
                computeFundPoolAggrRootByBill(blackboard.getAggrRootIdNotNull(payloadVO.getBillIdInKey()));
        if (fundPoolAggrRoot.isExist()) {
            blackboard.setGlobal(payloadVO.getPoolIdOutKey(), fundPoolAggrRoot.getIdStr());
            return Node.NodeStatus.SUCCESS;
        }
        return blackboard.getNodeStatus(actionNode.getId());
    }

    @Getter
    @Builder
    @Jacksonized
    public static class CreateFundPoolIfNotExistsFunctionPayload extends AbstractPayload {
        @FieldDescription("归集规则归属ID驶入键")
        @NotNull(message = "归集规则归属ID驶入键不能为空")
        private String collectRuleBelongIdInKey;

        @FieldDescription("资金账单ID驶入键")
        @NotNull(message = "资金账单ID驶入键不能为空")
        private String billIdInKey;

        @FieldDescription("资金池ID驶出键")
        @NotNull(message = "资金池ID驶出键不能为空")
        private String poolIdOutKey;
    }

}
