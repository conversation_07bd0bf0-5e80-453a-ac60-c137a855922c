package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req;

import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.BaseRequest;
import com.shouqianba.trade.fund.settlement.api.request.WithdrawCreateRequest;

import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2025/5/21 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class FundClearingWithdrawRequest extends BaseRequest {

    /**
     * 客户端流水号，代表唯一键
     */
    private String clientSn;

    /**
     * 提现金额（单位：分）
     * 当商户提现指定金额时上传
     */
    private Long amount;

    /**
     * 是否提现所有金额
     * 当商户提现所有时上传
     */
    private Boolean withdrawAllAmount;

    /**
     * 提现的商户ID
     */
    private String merchantId;

    /**
     * 品牌编号
     */
    private String brandSn;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 收单公司
     */
    private Integer acquiringCompany;

    /**
     * 统计时间
     */
    private String statisticsDate;

    public WithdrawCreateRequest genWithdrawCreateRequest() {
        return WithdrawCreateRequest.builder()
                .clientSn(clientSn)
                .amount(amount)
                .withdrawAllAmount(withdrawAllAmount)
                .merchantId(merchantId)
                .brandSn(brandSn)
                .merchantSn(merchantSn)
                .acquiringCompany(acquiringCompany)
                .statisticsDate(statisticsDate)
                .build();
    }
}
