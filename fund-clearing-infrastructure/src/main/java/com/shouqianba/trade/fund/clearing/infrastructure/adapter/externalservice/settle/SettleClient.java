package com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle;

import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.common.template.InvokeTemplate;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementBatchCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementFlowCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementFlowFinishCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingWithdrawRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementBatchCreateResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementFlowCreateResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementFlowFinishCreateResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingWithdrawResult;
import com.shouqianba.trade.fund.settlement.api.SettlementBatchService;
import com.shouqianba.trade.fund.settlement.api.SettlementFlowService;
import com.shouqianba.trade.fund.settlement.api.WithdrawService;
import com.shouqianba.trade.fund.settlement.api.request.SettlementBatchCreateRequest;
import com.shouqianba.trade.fund.settlement.api.request.SettlementFlowCreateFinishRequest;
import com.shouqianba.trade.fund.settlement.api.request.SettlementFlowCreateRequest;
import com.shouqianba.trade.fund.settlement.api.request.WithdrawCreateRequest;
import com.shouqianba.trade.fund.settlement.api.result.SettlementBatchCreateResult;
import com.shouqianba.trade.fund.settlement.api.result.SettlementFlowCreateResult;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawCreateResult;
import com.wosai.general.result.SingleResult;
import com.wosai.general.result.VoidResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/5/20 Time: 10:20 AM
 */
@Slf4j
@Component
public class SettleClient {

    @Resource
    private SettlementFlowService settlementFlowService;
    @Resource
    private SettlementBatchService settlementBatchService;
    @Resource
    private WithdrawService withdrawService;

    public FundClearingSettlementFlowCreateResult createSettlementFlow(
            FundClearingSettlementFlowCreateRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundClearingSettlementFlowCreateResult invoke(FundClearingSettlementFlowCreateRequest request) {
                SettlementFlowCreateRequest settlementFlowCreateRequest = request.genSettlementFlowCreateRequest();
                log.info("[调用结算系统推送资金流]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(settlementFlowCreateRequest));
                SingleResult<SettlementFlowCreateResult> result = settlementFlowService.createSettlementFlow(settlementFlowCreateRequest);
                log.info("[调用结算系统推送资金流]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                if (!result.isSuccess()) {
                    return FundClearingSettlementFlowCreateResult.FAILED;
                }
                return FundClearingSettlementFlowCreateResult.newInstance(result.getData());
            }

            @Override
            protected FundClearingSettlementFlowCreateResult onFailure(
                    FundClearingSettlementFlowCreateRequest request, Throwable throwable) {
                log.error("[调用结算系统推送资金流]>>>>>>异常栈:", throwable);
                return FundClearingSettlementFlowCreateResult.FAILED;
            }
        });
    }

    public FundClearingSettlementFlowFinishCreateResult finishCreateSettlementFlow(
            FundClearingSettlementFlowFinishCreateRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundClearingSettlementFlowFinishCreateResult invoke(
                    FundClearingSettlementFlowFinishCreateRequest request) {
                SettlementFlowCreateFinishRequest settlementFlowCreateFinishRequest =
                        request.genSettlementFlowCreateFinishRequest();
                log.info("[调用结算系统结束推送资金流]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(settlementFlowCreateFinishRequest));
                VoidResult result = settlementFlowService.finishCreateSettlementFlow(settlementFlowCreateFinishRequest);
                log.info("[调用结算系统结束推送资金流]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                if (!result.isSuccess()) {
                    return FundClearingSettlementFlowFinishCreateResult.FAILED;
                }
                return FundClearingSettlementFlowFinishCreateResult.SUCCESS;
            }

            @Override
            protected FundClearingSettlementFlowFinishCreateResult onFailure(
                    FundClearingSettlementFlowFinishCreateRequest request, Throwable throwable) {
                log.error("[调用结算系统结束推送资金流]>>>>>>异常栈:", throwable);
                return FundClearingSettlementFlowFinishCreateResult.FAILED;
            }
        });
    }

    public FundClearingSettlementBatchCreateResult createSettlementBatch(
            FundClearingSettlementBatchCreateRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundClearingSettlementBatchCreateResult invoke(FundClearingSettlementBatchCreateRequest request) {
                SettlementBatchCreateRequest batchCreateRequest = request.genSettlementBatchCreateRequest();
                log.info("[调用结算系统创建结算批次]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(batchCreateRequest));
                SingleResult<SettlementBatchCreateResult> result =
                        settlementBatchService.createSettlementBatch(batchCreateRequest);
                log.info("[调用结算系统创建结算批次]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));

                if (!result.isSuccess()) {
                    return FundClearingSettlementBatchCreateResult.FAILED;
                }
                return FundClearingSettlementBatchCreateResult.newInstance(result.getData());
            }

            @Override
            protected FundClearingSettlementBatchCreateResult onFailure(
                    FundClearingSettlementBatchCreateRequest request, Throwable throwable)
                    throws Throwable {
                log.error("[调用结算系统创建结算批次]>>>>>>异常栈:", throwable);
                return FundClearingSettlementBatchCreateResult.FAILED;
            }
        });
    }

    public FundClearingWithdrawResult createWithdraw(FundClearingWithdrawRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundClearingWithdrawResult invoke(FundClearingWithdrawRequest request) {
                WithdrawCreateRequest withdrawCreateRequest = request.genWithdrawCreateRequest();
                log.info("[调用结算系统提现]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(withdrawCreateRequest));
                SingleResult<WithdrawCreateResult> result =
                        withdrawService.createWithdraw(withdrawCreateRequest);
                log.info("[调用结算系统提现]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));

                if (!result.isSuccess()) {
                    return FundClearingWithdrawResult.DEFAULT;
                }
                return FundClearingWithdrawResult.newInstance(result.getData());
            }

            @Override
            protected FundClearingWithdrawResult onFailure(FundClearingWithdrawRequest request, Throwable throwable)
                    throws Throwable {
                log.error("[调用结算系统提现]>>>>>>异常栈:", throwable);
                return FundClearingWithdrawResult.DEFAULT;
            }
        });
    }


}
