<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbatch.FundBatchDao">
    <resultMap type="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbatch.po.FundBatchPO"
               id="FundBatchMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="payeeType" column="payee_type" jdbcType="TINYINT"/>
        <result property="brandSn" column="brand_sn" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="storeSn" column="store_sn" jdbcType="VARCHAR"/>
        <result property="payeeInfo" column="payee_info"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="billSource" column="bill_source" jdbcType="TINYINT"/>
        <result property="acquiringCompany" column="acquiring_company" jdbcType="SMALLINT"/>
        <result property="settleTime" column="settle_time" jdbcType="DATE"/>
        <result property="amount" column="amount"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="bizDomain" column="biz_domain"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="ext" column="ext"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="ctime" column="ctime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="table">
        fund_batch
    </sql>

    <sql id="allColumn">
        id,
        payee_type,
        brand_sn,
        merchant_sn,
        store_sn,
        payee_info,
        bill_source,
        acquiring_company,
        settle_time,
        amount,
        biz_domain,
        status,
        ext,
        ctime,
        mtime,
        version
    </sql>

    <sql id="insertColumnValue">
        #{id},
        #{payeeType},
        #{brandSn},
        #{merchantSn},
        #{storeSn},
        #{payeeInfo},
        #{billSource},
        #{acquiringCompany},
        #{settleTime},
        #{amount},
        #{bizDomain},
        #{status},
        #{ext},
        #{ctime},
        #{mtime},
        #{version}
    </sql>

    <insert id="insert">
        insert into
        <include refid="table"/>
        (
        <include refid="allColumn"/>
        )
        values
        (
        <include refid="insertColumnValue"/>
        )
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="table"/>
        (
        <include refid="allColumn"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.payeeType},
            #{item.brandSn},
            #{item.merchantSn},
            #{item.storeSn},
            #{item.payeeInfo},
            #{item.billSource},
            #{item.acquiringCompany},
            #{item.settleTime},
            #{item.amount},
            #{item.bizDomain},
            #{item.status},
            #{item.ext},
            #{item.ctime},
            #{item.mtime},
            #{item.version}
            )
        </foreach>
    </insert>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <if test="payeeType != null">
                payee_type = #{payeeType},
            </if>
            <if test="brandSn != null">
                brand_sn = #{brandSn},
            </if>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn},
            </if>
            <if test="storeSn != null">
                store_sn = #{storeSn},
            </if>
            <if test="payeeInfo != null">
                payee_info = #{payeeInfo},
            </if>
            <if test="billSource != null">
                bill_source = #{billSource},
            </if>
            <if test="acquiringCompany != null">
                acquiring_company = #{acquiringCompany},
            </if>
            <if test="settleTime != null">
                settle_time = #{settleTime},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="bizDomain != null">
                biz_domain = #{bizDomain},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="ext != null">
                ext = #{ext},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="select" resultMap="FundBatchMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null">
                and store_sn = #{storeSn}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="acquiringCompany != null">
                and acquiring_company = #{acquiringCompany}
            </if>
            <if test="settleTime != null">
                and settle_time = #{settleTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectForUpdate" resultMap="FundBatchMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update
    </select>

    <select id="batchSelect" resultMap="FundBatchMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="payeeType != null">
                and payee_type = #{payeeType}
            </if>
            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="acquiringCompany != null">
                AND acquiring_company = #{acquiringCompany}
            </if>
            <if test="settleTime != null">
                and settle_time = #{settleTime}
            </if>
            <if test="settleTimeLessThanOrEqual != null">
                and settle_time &lt;= #{settleTimeLessThanOrEqual}
            </if>
        </where>
        <if test="sortField != null">
            order by ${sortField}
            <if test="isDesc == true">
                desc
            </if>
            <if test="isDesc == false">
                asc
            </if>
        </if>
        <if test="querySize != null">
            limit #{querySize}
        </if>
    </select>

    <select id="count" parameterType="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbatch.model.FundBatchQuery"
            resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM fund_batch
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND id IN
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="payeeType != null">
                AND payee_type = #{payeeType}
            </if>
            <if test="brandSn != null and brandSn != ''">
                AND brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null and merchantSn != ''">
                AND merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null and storeSn != ''">
                AND store_sn = #{storeSn}
            </if>
            <if test="billSource != null">
                AND bill_source = #{billSource}
            </if>
            <if test="acquiringCompany != null">
                AND acquiring_company = #{acquiringCompany}
            </if>
            <if test="settleTime != null">
                AND settle_time = #{settleTime}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND status IN
                <foreach collection="statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="settleTimeLessThanOrEqual != null">
                and settle_time &lt;= #{settleTimeLessThanOrEqual}
            </if>
        </where>
    </select>

    <select id="selectForUpdateSkipLocked" resultMap="FundBatchMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update skip locked
    </select>

</mapper>
