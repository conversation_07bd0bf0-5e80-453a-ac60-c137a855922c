<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.FundPoolDao">

    <resultMap type="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundpool.po.FundPoolPO" id="FundPoolMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="batchId" column="batch_id" jdbcType="BIGINT"/>
        <result property="payeeType" column="payee_type" jdbcType="TINYINT"/>
        <result property="brandSn" column="brand_sn" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="storeSn" column="store_sn" jdbcType="VARCHAR"/>
        <result property="payeeInfo" column="payee_info"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="acquiringCompany" column="acquiring_company" jdbcType="INTEGER"/>
        <result property="billSource" column="bill_source" jdbcType="TINYINT"/>
        <result property="settleDate" column="settle_date"
                typeHandler="org.apache.ibatis.type.LocalDateTypeHandler"/>
        <result property="amount" column="amount"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="bizDomain" column="biz_domain"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="ext" column="ext"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="ctime" column="ctime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="table">
        fund_pool
    </sql>

    <sql id="allColumn">
        id,
        batch_id,
        payee_type,
        brand_sn,
        merchant_sn,
        store_sn,
        payee_info,
        acquiring_company,
        bill_source,
        settle_date,
        amount,
        biz_domain,
        status,
        ext,
        ctime,
        mtime,
        version
    </sql>


    <sql id="insertColumnValue">
        #{id},
        #{batchId},
        #{payeeType},
        #{brandSn},
        #{merchantSn},
        #{storeSn},
        #{payeeInfo},
        #{acquiringCompany},
        #{billSource},
        #{settleDate},
        #{amount},
        #{bizDomain},
        #{status},
        #{ext},
        #{ctime},
        #{mtime},
        #{version}
    </sql>


    <insert id="insert">
        insert into
        <include refid="table"/>
        (
            <include refid="allColumn"/>
        )
        values
        (
            <include refid="insertColumnValue"/>
        )
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="table"/>
        (
            <include refid="allColumn"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},#{item.batchId}, #{item.payeeType}, #{item.brandSn}, #{item.merchantSn}, #{item.storeSn}, #{item.payeeInfo}
                , #{item.acquiringCompany}, #{item.billSource}, #{item.settleDate}, #{item.amount}, #{item.bizDomain}, #{item.status}
                , #{item.ext}, #{item.ctime}, #{item.mtime}
                , #{item.version}
            )
        </foreach>
    </insert>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <if test="payeeInfo != null">
                payee_info = #{payeeInfo},
            </if>
            <if test="acquiringCompany != null">
                acquiring_company = #{acquiringCompany},
            </if>
            <if test="billSource != null">
                bill_source = #{billSource},
            </if>
            <if test="settleDate != null">
                settle_date = #{settleDate},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="bizDomain != null">
                biz_domain = #{bizDomain},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="ext != null">
                ext = #{ext},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="select" resultMap="FundPoolMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="batchId != null">
                and batch_id = #{batchId}
            </if>
            <if test="payeeType != null">
                and payee_type = #{payeeType}
            </if>

            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null">
                and store_sn = #{storeSn}
            </if>
            <if test="acquiringCompany != null">
              and acquiring_company = #{acquiringCompany}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="settleDate != null">
                and   settle_date = #{settleDate}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="statusItem" open="(" separator="," close=")">
                    #{statusItem}
                </foreach>
            </if>
        </where>
        limit 1
    </select>

    <select id="batchSelect" resultMap="FundPoolMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="batchId != null">
                and batch_id = #{batchId}
            </if>
            <if test="payeeType != null">
                and payee_type = #{payeeType}
            </if>
            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null">
                and store_sn = #{storeSn}
            </if>
            <if test="acquiringCompany != null">
                and acquiring_company = #{acquiringCompany}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="settleDate != null">
               and settle_date = #{settleDate}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="statusItem" open="(" separator="," close=")">
                    #{statusItem}
                </foreach>
            </if>
            <if test="cursorField != null and endCursor != null">
                <if test="isDesc == false">
                    and `${cursorField}` <![CDATA[>]]> #{endCursor}
                </if>
                <if test="isDesc == true">
                    and `${cursorField}` <![CDATA[<]]> #{endCursor}
                </if>
            </if>
        </where>
        <if test="sortField != null and isDesc != null">
            order by ${sortField} <if test="isDesc">desc</if><if test="!isDesc">asc</if>
        </if>
        <if test="querySize != null">
            limit #{querySize}
        </if>
    </select>
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from
        <include refid="table"/>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="batchId != null">
                and batch_id = #{batchId}
            </if>
            <if test="payeeType != null">
                and payee_type = #{payeeType}
            </if>
            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null">
                and store_sn = #{storeSn}
            </if>
            <if test="acquiringCompany != null">
                and acquiring_company = #{acquiringCompany}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="settleDate != null">
                and settle_date = #{settleDate}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="statusItem" open="(" separator="," close=")">
                    #{statusItem}
                </foreach>
            </if>
            <if test="cursorField != null and endCursor != null">
                <if test="isDesc == false">
                    and `${cursorField}` <![CDATA[>]]> #{endCursor}
                </if>
                <if test="isDesc == true">
                    and `${cursorField}` <![CDATA[<]]> #{endCursor}
                </if>
            </if>
        </where>
    </select>
</mapper>
