<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbill.FundBillDao">
    <resultMap type="com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbill.po.FundBillPO"
               id="FundBillMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="acquiringDate" column="acquiring_date" jdbcType="DATE"/>
        <result property="billDate" column="bill_date" jdbcType="DATE"/>
        <result property="payeeType" column="payee_type" jdbcType="TINYINT"/>
        <result property="brandSn" column="brand_sn" jdbcType="VARCHAR"/>
        <result property="merchantSn" column="merchant_sn" jdbcType="VARCHAR"/>
        <result property="storeSn" column="store_sn" jdbcType="VARCHAR"/>
        <result property="payeeInfo" column="payee_info"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="billSource" column="bill_source" jdbcType="TINYINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="amount" column="amount"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="transSn" column="trans_sn" jdbcType="VARCHAR"/>
        <result property="orderSn" column="order_sn" jdbcType="VARCHAR"/>
        <result property="tradeDomain" column="trade_domain"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="bizDomain" column="biz_domain"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="ext" column="ext"
                typeHandler="com.shouqianba.trade.fund.clearing.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="ctime" column="ctime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="fundPoolId" column="fund_pool_id" jdbcType="BIGINT"/>
        <result property="fundBatchId" column="fund_batch_id" jdbcType="BIGINT"/>

    </resultMap>

    <sql id="table">
        fund_bill
    </sql>

    <sql id="allColumn">
        id
        ,
        fund_pool_id,
        fund_batch_id,
        acquiring_date,
        bill_date,
        payee_type,
        brand_sn,
        merchant_sn,
        store_sn,
        payee_info,
        bill_source,
        `type`,
        `status`,
        amount,
        trans_sn,
        order_sn,
        trade_domain,
        biz_domain,
        ext,
        ctime,
        mtime,
        version
    </sql>

    <sql id="insertColumnValue">
        #{id}
        ,
        #{fundPoolId},
        #{fundBatchId},
        #{acquiringDate},
        #{billDate},
        #{payeeType},
        <!-- 删除 #{payeeSn}, -->
        #{brandSn},
        #{merchantSn},
        #{storeSn},
        #{payeeInfo},
        #{billSource},
        #{type},
        #{status},
        #{amount},
        #{transSn},
        #{orderSn},
        #{tradeDomain},
        #{bizDomain},
        #{ext},
        #{ctime},
        #{mtime},
        #{version}
    </sql>

    <insert id="insert">
        insert into
        <include refid="table"/>
        (
        <include refid="allColumn"/>
        )
        values
        (
        <include refid="insertColumnValue"/>
        )
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="table"/>
        (
        <include refid="allColumn"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.fundPoolId}, #{item.fundBatchId}, #{item.acquiringDate}, #{item.billDate}, #{item.payeeType},
            #{item.brandSn}, #{item.merchantSn}, #{item.storeSn},
            #{item.payeeInfo}, #{item.billSource}, #{item.type}, #{item.status}
            , #{item.amount}, #{item.transSn}, #{item.orderSn}, #{item.tradeDomain}
            , #{item.bizDomain}, #{item.ext}, #{item.ctime}, #{item.mtime}
            , #{item.version}
            )
        </foreach>
    </insert>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <if test="acquiringDate != null">
                acquiring_date = #{acquiringDate},
            </if>
            <if test="billDate != null">
                bill_date = #{billDate},
            </if>
            <if test="payeeType != null">
                payee_type = #{payeeType},
            </if>
            <if test="brandSn != null">
                brand_sn = #{brandSn},
            </if>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn},
            </if>
            <if test="storeSn != null">
                store_sn = #{storeSn},
            </if>
            <if test="payeeInfo != null">
                payee_info = #{payeeInfo},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="bizDomain != null">
                biz_domain = #{bizDomain},
            </if>
            <if test="ext != null">
                ext = #{ext},
            </if>
            <if test="fundPoolId != null">
                fund_pool_id = #{fundPoolId},
            </if>
            <if test="fundBatchId != null or updateFundBatchIdToNull == true">
                fund_batch_id = #{fundBatchId},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="select" resultMap="FundBillMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="id != null">
                    id = #{id}
                    <if test="status != null">
                        and status = #{status}
                    </if>
                </when>
                <when test="transSn != null and billSource != null">
                    trans_sn = #{transSn} and bill_source = #{billSource}
                </when>
                <otherwise>
                    <if test="acquiringDate != null">
                        and acquiring_date = #{acquiringDate}
                    </if>
                    <if test="billDate != null">
                        and bill_date = #{billDate}
                    </if>
                    <if test="payeeType != null">
                        and payee_type = #{payeeType}
                    </if>
                    <if test="brandSn != null">
                        and brand_sn = #{brandSn}
                    </if>
                    <if test="orderSn != null">
                        and order_sn = #{orderSn}
                    </if>
                    <if test="merchantSn != null">
                        and merchant_sn = #{merchantSn}
                    </if>
                    <if test="storeSn != null">
                        and store_sn = #{storeSn}
                    </if>
                    <!-- 其他查询条件保持不变 -->
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectForUpdate" resultMap="FundBillMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update
    </select>

    <select id="selectForUpdateSkipLocked" resultMap="FundBillMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update skip locked
    </select>

    <select id="batchSelect" resultMap="FundBillMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="acquiringDate != null">
                and acquiring_date = #{acquiringDate}
            </if>
            <if test="billDate != null">
                and bill_date = #{billDate}
            </if>
            <if test="payeeType != null">
                and payee_type = #{payeeType}
            </if>
            <if test="fundPoolId != null">
                and fund_pool_id = #{fundPoolId}
            </if>
            <if test="fundBatchId != null">
                and fund_batch_id = #{fundBatchId}
            </if>
            <if test="type != null">
                and `type` = #{type}
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="fundPoolIds != null and fundPoolIds.size() > 0">
                and fund_pool_id in
                <foreach collection="fundPoolIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null">
                and store_sn = #{storeSn}
            </if>
            <if test="orderSn != null">
                and order_sn = #{orderSn}
            </if>
            <if test="transSn != null">
                and trans_sn = #{transSn}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="cursorField != null and endCursor != null">
                <if test="isDesc == false">
                    and `${cursorField}` <![CDATA[>]]> #{endCursor}
                </if>
                <if test="isDesc == true">
                    and `${cursorField}` <![CDATA[<]]> #{endCursor}
                </if>
            </if>
        </where>
        <if test="sortField != null">
            order by `${sortField}`
            <if test="isDesc == false">
                asc
            </if>
            <if test="isDesc == true">
                desc
            </if>
        </if>
        <choose>
            <!--偏移量分页-->
            <when test="offset != null and querySize != null">
                limit ${offset}, ${querySize}
            </when>
            <when test="offset == null and querySize != null">
                limit ${querySize}
            </when>
            <otherwise>
                limit 1000
            </otherwise>
        </choose>
    </select>

    <select id="count" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM
        <include refid="table"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="acquiringDate != null">
                and acquiring_date = #{acquiringDate}
            </if>
            <if test="billDate != null">
                and bill_date = #{billDate}
            </if>
            <if test="payeeType != null">
                and payee_type = #{payeeType}
            </if>
            <if test="fundPoolId != null">
                and fund_pool_id = #{fundPoolId}
            </if>
            <if test="fundBatchId != null">
                and fund_batch_id = #{fundBatchId}
            </if>
            <if test="type != null">
                and `type` = #{type}
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="fundPoolIds != null and fundPoolIds.size() > 0">
                and fund_pool_id in
                <foreach collection="fundPoolIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="brandSn != null">
                and brand_sn = #{brandSn}
            </if>
            <if test="merchantSn != null">
                and merchant_sn = #{merchantSn}
            </if>
            <if test="storeSn != null">
                and store_sn = #{storeSn}
            </if>
            <if test="orderSn != null">
                and order_sn = #{orderSn}
            </if>
            <if test="transSn != null">
                and trans_sn = #{transSn}
            </if>
            <if test="billSource != null">
                and bill_source = #{billSource}
            </if>
            <if test="cursorField != null and endCursor != null">
                <if test="isDesc == false">
                    and `${cursorField}` <![CDATA[>]]> #{endCursor}
                </if>
                <if test="isDesc == true">
                    and `${cursorField}` <![CDATA[<]]> #{endCursor}
                </if>
            </if>
        </where>
    </select>
</mapper>
