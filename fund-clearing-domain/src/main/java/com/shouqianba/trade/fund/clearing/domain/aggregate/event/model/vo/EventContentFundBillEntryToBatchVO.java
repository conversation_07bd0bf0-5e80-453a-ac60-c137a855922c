package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2023/7/30 Time: 10:11
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentFundBillEntryToBatchVO extends BaseVO<EventContentFundBillEntryToBatchVO> {

    @Override
    protected EventContentFundBillEntryToBatchVO doReplaceNotNull(EventContentFundBillEntryToBatchVO vo) {
        EventContentFundBillEntryToBatchVO.EventContentFundBillEntryToBatchVOBuilder builder = toBuilder();
        return builder.build();
    }

}
