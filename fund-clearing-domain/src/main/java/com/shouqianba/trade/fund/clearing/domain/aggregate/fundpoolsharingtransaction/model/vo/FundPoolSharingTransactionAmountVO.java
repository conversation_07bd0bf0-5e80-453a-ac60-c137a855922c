package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

@Getter
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FundPoolSharingTransactionAmountVO extends BaseVO<FundPoolSharingTransactionAmountVO> {
    @NotNull(message = "原始金额不能为空")
    private final Long originAmount;

    @NotNull(message = "手续费不能为空")
    private final Long fee;

    @NotNull(message = "分账基础金额不能为空")
    private final Long sharingBaseAmount;

    private final Long sharingAmount;

    @Override
    protected FundPoolSharingTransactionAmountVO doReplaceNotNull(FundPoolSharingTransactionAmountVO vo) {
        FundPoolSharingTransactionAmountVO.FundPoolSharingTransactionAmountVOBuilder builder = toBuilder();

        if (vo.getSharingAmount() != null) {
            builder.sharingAmount(vo.getSharingAmount());
        }

        return builder.build();
    }
}