package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseEntity;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.enums.FundPoolSharingTransBookStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.vo.FundPoolSharingTransBookAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.FundTransactionTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class FundPoolSharingTransBookAggrRoot extends BaseEntity {
    @NotNull(message = "分账流水ID不能为空")
    private Long transactionId;

    @NotNull(message = "资金交易类型不能为空")
    private FundTransactionTypeEnum type;

    @NotNull(message = "收款方对象类型不能为空")
    private AccountTypeEnum payeeType;

    private String brandSn;
    private String merchantSn;
    private String storeSn;

    @Valid
    private PayeeInfoVO payeeInfo;

    @NotNull(message = "分账接收方对象类型不能为空")
    private AccountTypeEnum receiverType;

    @NotBlank(message = "分账接收方对象编号不能为空")
    private String receiverSn;

    @Valid
    private PayeeInfoVO receiverInfo;

    @Valid
    @NotNull(message = "金额信息不能为空")
    private FundPoolSharingTransBookAmountVO amount;

    @NotNull(message = "状态不能为空")
    private FundPoolSharingTransBookStatusEnum status;

    protected FundPoolSharingTransBookAggrRoot() {
    }

    public static FundPoolSharingTransBookAggrRoot newEmptyInstance() {
        return new FundPoolSharingTransBookAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_POOL_SHARING_TRANS_BOOK_NOT_EXIST);
        }
    }


    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public void updateStatus(FundPoolSharingTransBookStatusEnum FundPoolSharingTransBookStatusEnum) {
        if (Objects.nonNull(FundPoolSharingTransBookStatusEnum)) {
            this.status = FundPoolSharingTransBookStatusEnum;
        }
        modifyUpdated();
    }

    public Long getSharingAmount() {
        return amount.getSharingAmount();
    }

}