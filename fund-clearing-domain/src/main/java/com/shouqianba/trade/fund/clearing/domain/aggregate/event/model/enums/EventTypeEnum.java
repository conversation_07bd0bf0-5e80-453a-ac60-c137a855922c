package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:57 PM
 */
@Getter
public enum EventTypeEnum {
    // 报备相关
    BRAND_ENROLL((byte) 1, "品牌报备"),

    // 入账相关
    FUND_CHANNEL_NOTIFY((byte) 2, "资金动账通知"),
    CLEARING_NOTIFY((byte) 3, "清分通知"),
    BILL_ENTRY_TO_BATCH((byte) 4, "账单入账转批次"),
    FULL_CLEARING((byte) 5, "资金动账全额清分"),
    PARTIAL_CLEARING((byte) 6, "资金动账部分清分"),
    CREATE_SETTLEMENT_FLOW((byte) 7, "创建结算流水"),
    CREATE_SETTLEMENT_FLOW_MONITOR((byte) 8, "创建结算流水监控"),
    FUND_BATCH_BILL_ENTRY_TO_POOL((byte) 9, "资金池入账转账单"),
    FUND_BATCH_BILL_ENTRY_TO_POOL_MONITOR((byte) 10, "资金池入账转账单监控"),
    FUND_BILL_ENTRY_TO_POOL((byte) 11, "资金池入账转账单"),

    // 分账相关
    BATCH_SHARING_START((byte) 10, "批次开始分账"),
    SHARING_START((byte) 11, "开始分账"),
    GEN_ORDER_FORWARD_TRANSACTION((byte) 12, "生成订单分账正向交易"),
    GEN_WALLET_FORWARD_TRANSACTION((byte) 13, "生成余额分账正向交易"),
    UPDATE_ORDER_RULE_FORWARD_TRANSACTION((byte) 14, "更新订单规则分账交易"),
    UPDATE_ORDER_API_FORWARD_TRANSACTION((byte) 15, "更新订单API分账交易"),
    GEN_SHARING_BOOK((byte) 16, "生成清分账本"),
    SHARING_SINGLE_DELAY_BILL((byte) 17, "单个延迟账单清分"),
    SHARING_SINGLE_REFUND_BILL((byte) 18, "单个退款账单分账回退"),
    POOL_REFUND_MONITOR((byte) 19, "池维度分账回退是否成功"),
    TRANSACTION_SHARING_BOOK_SETTLEMENT((byte) 20, "交易分账单推送结算"),
    POOL_SHARING_BOOK_SETTLEMENT((byte) 21, "池分账单推送结算");


    //涉及外部调用的事件，需事件处理器独立处理事务性，不走统一处理
    private static final Set<EventTypeEnum> IS_INDEPENDENT_PROCESS_EVENTS = Set.of( SHARING_START, GEN_ORDER_FORWARD_TRANSACTION, GEN_WALLET_FORWARD_TRANSACTION
            , UPDATE_ORDER_RULE_FORWARD_TRANSACTION, UPDATE_ORDER_API_FORWARD_TRANSACTION, GEN_SHARING_BOOK, SHARING_SINGLE_DELAY_BILL, SHARING_SINGLE_REFUND_BILL, POOL_REFUND_MONITOR
            , POOL_SHARING_BOOK_SETTLEMENT);

    private static final Set<EventTypeEnum> MCH_HOT_ACCOUNT_EVENTS = Set.of( SHARING_START, GEN_ORDER_FORWARD_TRANSACTION, GEN_WALLET_FORWARD_TRANSACTION
            , UPDATE_ORDER_RULE_FORWARD_TRANSACTION, UPDATE_ORDER_API_FORWARD_TRANSACTION, GEN_SHARING_BOOK, SHARING_SINGLE_DELAY_BILL, SHARING_SINGLE_REFUND_BILL, POOL_REFUND_MONITOR
            , POOL_SHARING_BOOK_SETTLEMENT);


    private final byte code;
    private final String desc;


    EventTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EventTypeEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        EventTypeEnum[] typeEnums = EventTypeEnum.values();
        for (EventTypeEnum type : typeEnums) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }

        return null;
    }

    public static boolean isNeedIndependentProcess(EventTypeEnum type) {
        return IS_INDEPENDENT_PROCESS_EVENTS.contains(type);
    }

    public static boolean isMchHotAccountEvent(EventTypeEnum type) {
        return MCH_HOT_ACCOUNT_EVENTS.contains(type);
    }

}
