package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseEntity;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.PaywayEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class FundPoolAggrRoot extends BaseEntity {

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @NotNull(message = "收款方主体类型不能为空")
    private AccountTypeEnum payeeType;

    @NotEmpty(message = "收款方主体编号不能为空")
    private String brandSn;

    @NotEmpty(message = "商户编号不能为空")
    private String merchantSn;

    @NotNull(message = "批次ID不能为空")
    private Long batchId;

    private String storeSn;

    @Valid
    private PayeeInfoVO payeeInfo;

    @NotNull(message = "账单来源不能为空")
    private BillSourceEnum billSource;

    @NotNull(message = "收单机构不能为空")
    private Integer acquiringCompany;

    @NotNull(message = "结算时间不能为空")
    private LocalDate settleDate;

    @Valid
    @NotNull(message = "金额信息不能为空")
    private FundPoolAmountVO amount;

    @Valid
    @NotNull(message = "业务领域信息不能为空")
    private FundPoolBizDomainVO bizDomain;

    @Valid
    private FundPoolExtVO ext;

    @NotNull(message = "状态不能为空")
    private FundPoolStatusEnum status;

    protected FundPoolAggrRoot() {
    }

    public static FundPoolAggrRoot newEmptyInstance() {
        return new FundPoolAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_POOL_NOT_EXIST);
        }
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public void updateStatus(FundPoolStatusEnum status) {
        if (Objects.nonNull(status)) {
            this.status = status;
            this.modifyUpdated();
        }
    }

    public void updateEntryActionStatus(FundPoolBizDomainVO.EntryActionStatusVO vo) {
        if (Objects.nonNull(vo)) {
            bizDomain = bizDomain.replaceNotNull(FundPoolBizDomainVO
                    .builder()
                    .entryActionStatus(vo)
                    .build());
            this.modifyUpdated();
        }
    }

    public PaywayEnum getPayway() {
        return bizDomain.getPayway();
    }

    public void updateSharingActionStatus(FundPoolBizDomainVO.SharingActionStatusVO vo) {
        if (Objects.nonNull(vo)) {
            bizDomain = bizDomain.replaceNotNull(FundPoolBizDomainVO
                    .builder()
                    .sharingActionStatus(vo)
                    .build());
            this.modifyUpdated();
        }
    }

    public void updateByFundBillAmount(FundBillAggrRoot fundBillAggrRoot) {
        FundBillAmountVO billAmount = fundBillAggrRoot.getAmount();
        if (Objects.equals(fundBillAggrRoot.getType(), FundBillTypeEnum.PAYMENT)) {
            this.amount = this.amount.replaceNotNull(FundPoolAmountVO
                    .builder()
                    .originAmount(this.amount.getOriginAmountAdded(
                            billAmount.getOriginAmount()))
                    .fee(this.amount.getFeeAdded(billAmount.getFeeNullToZero()))
                    .payOriginAmount(this.amount
                            .getPayOriginAmountAdded(billAmount.getOriginAmount()))
                    .payFee(this.amount.getPayFeeAdded(billAmount.getFeeNullToZero()))
                    .build());

        } else {
            this.amount = this.amount.replaceNotNull(FundPoolAmountVO
                    .builder()
                    .originAmount(this.amount.getOriginAmountAdded(
                            billAmount.getOriginAmount()))
                    .fee(this.amount.getFeeAdded(
                            billAmount.getFeeNullToZero()))
                    .refundOriginAmount(this.amount.getRefundOriginAmountAdded(billAmount.getOriginAmount()))
                    .refundFee(this.amount.getRefundFeeAdded(billAmount.getFeeNullToZero()))
                    .build());
        }

        this.modifyUpdated();
    }

    public Long getBillEntrySettleBehaviorTreeId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getBillEntrySettleBehaviorTreeId();
    }

    public Long getFundCollectRuleBelongId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getFundCollectRuleBelongId();
    }

    /**
     * 更新行为树ID
     **/
    public void updateBtId(Long sharingBtId) {
        if (Objects.isNull(bizDomain.getSharingBtId())) {
            bizDomain = bizDomain.replaceNotNull(
                    FundPoolBizDomainVO
                            .builder()
                            .sharingBtId(sharingBtId)
                            .build());
            modifyUpdated();
        }
    }

    public boolean isEntrySettlementFailed() {
        if (Objects.isNull(bizDomain)) {
            return false;
        }
        return bizDomain.isEntrySettlementFailed();
    }

    public boolean isEntrySettlementSucceed() {
        if (Objects.isNull(bizDomain)) {
            return false;
        }
        return bizDomain.isEntrySettlementSucceed();
    }

    public boolean isEntrySettled() {
        return FundPoolStatusEnum.isEntrySettled(status);
    }

    public String getFundCollectRuleBelongIdStr() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getFundCollectRuleBelongIdStr();
    }

    public boolean isSettlementInProcess() {
        return Objects.equals(status, FundPoolStatusEnum.ENTRY_SETTLEMENT_IN_PROGRESS);
    }

    public Long getBehaviorTreeId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getBillEntrySettleBehaviorTreeId();
    }


    public boolean isSettlementCompleted() {
        return Objects.equals(status, FundPoolStatusEnum.SETTLEMENT_COMPLETED);
    }

    public void updateAmount(FundPoolAmountVO fundPoolAmountVO) {
        if (Objects.nonNull(fundPoolAmountVO)) {
            this.amount = amount.replaceNotNull(fundPoolAmountVO);
            this.modifyUpdated();
        }
    }

}
