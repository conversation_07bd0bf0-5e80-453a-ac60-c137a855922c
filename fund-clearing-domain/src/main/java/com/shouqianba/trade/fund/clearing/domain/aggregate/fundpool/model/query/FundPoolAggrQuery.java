package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query;

import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Builder
public class FundPoolAggrQuery {
    private Long id;
    private Long batchId;
    private AccountTypeEnum payeeType;
    private String brandSn;
    private String merchantSn;
    private String storeSn;
    private Integer acquiringCompany;
    private BillSourceEnum billSource;
    private LocalDate settleDate;
    private FundPoolStatusEnum status; // 单个状态查询
    private List<FundPoolStatusEnum> statusList; // 多个状态查询
    private String sortField;
    private boolean isDesc;
    private Integer querySize;

    private String cursorField;
    private String endCursor;
}
