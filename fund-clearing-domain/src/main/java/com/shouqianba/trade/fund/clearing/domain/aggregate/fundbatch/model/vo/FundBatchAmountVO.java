package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillAmountVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;

/**
 * 资金批次金额信息VO
 *
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class FundBatchAmountVO extends BaseVO<FundBatchAmountVO> {

    @NotNull(message = "总金额不能为空")
    private final Long totalAmount;

    private final Long feeAmount;

    private final Long settleAmount;

    public static FundBatchAmountVO newEmptyInstance() {
        return FundBatchAmountVO
                .builder()
                .totalAmount(0L)
                .build();
    }

    @Override
    protected FundBatchAmountVO doReplaceNotNull(FundBatchAmountVO vo) {
        FundBatchAmountVO.FundBatchAmountVOBuilder builder = toBuilder();

        Long totalAmount = vo.getTotalAmount();
        if (Objects.nonNull(totalAmount)) {
            builder.totalAmount(totalAmount);
        }

        Long feeAmount = vo.getFeeAmount();
        if (Objects.nonNull(feeAmount)) {
            builder.feeAmount(feeAmount);
        }

        Long settleAmount = vo.getSettleAmount();
        if (Objects.nonNull(settleAmount)) {
            builder.settleAmount(settleAmount);
        }

        return builder.build();
    }

    @JsonIgnore
    public Long getFeeAmountDefaultZero() {
        return Optional
                .ofNullable(feeAmount)
                .orElse(0L);
    }

    @JsonIgnore
    public Long getSettleAmountDefaultZero() {
        return Optional
                .ofNullable(settleAmount)
                .orElse(0L);
    }

    @JsonIgnore
    public Long getTotalAmountDefaultZero() {
        return Optional
                .ofNullable(totalAmount)
                .orElse(0L);
    }

    @JsonIgnore
    public Long getNeedClearingAmount() {
        return getTotalAmountDefaultZero() - getFeeAmountDefaultZero();
    }

    @JsonIgnore
    public Long getTotalAmountAdded( Long originAmount) {
        if (Objects.isNull(originAmount)) {
            return getTotalAmountDefaultZero();
        }
        return getTotalAmountDefaultZero() + originAmount;
    }

    @JsonIgnore
    public Long getFeeAdded(Long fee) {
        if (Objects.isNull(fee)) {
            return getFeeAmountDefaultZero();
        }
        return getFeeAmountDefaultZero() + fee;
    }

    @JsonIgnore
    public Long getSettleAmountAdded() {
       return getTotalAmountDefaultZero() + getFeeAmountDefaultZero();
    }

    public FundBatchAmountVO add(FundBillAmountVO amount, boolean isRefund) {
        if (Objects.isNull(amount)) {
            return this;
        }
        return toBuilder()
               .totalAmount(getTotalAmountAdded(isRefund? -amount.getOriginAmount(): amount.getOriginAmount()))
               .feeAmount(getFeeAdded(isRefund? -amount.getFee(): amount.getFee()))
               .build();
    }
}
