package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/30 Time: 10:11
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentFundBatchBillEntryToPoolMonitorVO extends BaseVO<EventContentFundBatchBillEntryToPoolMonitorVO> {

    private Long blackboardId;


    @Override
    protected EventContentFundBatchBillEntryToPoolMonitorVO doReplaceNotNull(
            EventContentFundBatchBillEntryToPoolMonitorVO vo) {
        EventContentFundBatchBillEntryToPoolMonitorVO.EventContentFundBatchBillEntryToPoolMonitorVOBuilder builder = toBuilder();

        Long blackboardId = vo.getBlackboardId();
        if (Objects.nonNull(blackboardId)) {
            builder.blackboardId(blackboardId);
        }

        return builder.build();
    }

}
