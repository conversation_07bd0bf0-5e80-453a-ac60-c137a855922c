package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SharingBasisEnum;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class FundPoolAmountVO extends BaseVO<FundPoolAmountVO> {

    @NotNull(message = "总金额不能为空")
    private final Long originAmount;
    @NotNull(message = "总费用不能为空")
    private final Long fee;
    @NotNull(message = "支付总金额不能为空")
    private final Long payOriginAmount;
    @NotNull(message = "支付总手续费用不能为空")
    private final Long payFee;
    @NotNull(message = "退款总金额不能为空")
    private final Long refundOriginAmount;
    @NotNull(message = "退款总手续费用不能为空")
    private final Long refundFee;

    private final FundPoolAmountDiscountVO discount;

    public static FundPoolAmountVO newEmptyInstance() {
        return FundPoolAmountVO
                .builder()
                .originAmount(0L)
                .fee(0L)
                .payOriginAmount(0L)
                .payFee(0L)
                .refundOriginAmount(0L)
                .refundFee(0L)
                .discount(FundPoolAmountDiscountVO.newEmptyInstance())
                .build();
    }

    @Override
    protected FundPoolAmountVO doReplaceNotNull(FundPoolAmountVO vo) {
        FundPoolAmountVOBuilder builder = toBuilder();

        Long originAmount = vo.getOriginAmount();
        if (Objects.nonNull(originAmount)) {
            builder.originAmount(originAmount);
        }
        Long fee = vo.getFee();
        if (Objects.nonNull(fee)) {
            builder.fee(fee);
        }
        FundPoolAmountDiscountVO discount = vo.getDiscount();
        if (Objects.nonNull(discount)) {
            builder.discount(discount);
        }
        Long payOriginAmount = vo.getPayOriginAmount();
        if (Objects.nonNull(payOriginAmount)) {
            builder.payOriginAmount(payOriginAmount);
        }
        Long payFee = vo.getPayFee();
        if (Objects.nonNull(payFee)) {
            builder.payFee(payFee);
        }
        Long refundOriginAmount = vo.getRefundOriginAmount();
        if (Objects.nonNull(refundOriginAmount)) {
            builder.refundOriginAmount(refundOriginAmount);
        }
        Long refundFee = vo.getRefundFee();
        if (Objects.nonNull(refundFee)) {
            builder.refundFee(refundFee);
        }

        return builder.build();
    }

    @JsonIgnore
    public Long getOriginAmountAdded(Long originAmount) {
        if (Objects.isNull(this.originAmount)) {
            return originAmount;
        }
        if (Objects.isNull(originAmount)) {
            return this.originAmount;
        }
        return this.originAmount + originAmount;
    }

    @JsonIgnore
    public Long getFeeAdded(Long fee) {
        if (Objects.isNull(this.fee)) {
            return fee;
        }
        if (Objects.isNull(fee)) {
            return this.fee;
        }
        return this.fee + fee;
    }

    @JsonIgnore
    public Long getPayOriginAmountAdded(Long payOriginAmount) {
        if (Objects.isNull(this.payOriginAmount)) {
            return payOriginAmount;
        }
        if (Objects.isNull(payOriginAmount)) {
            return this.payOriginAmount;
        }
        return this.payOriginAmount + payOriginAmount;
    }

    @JsonIgnore
    public Long getPayFeeAdded(Long payFee) {
        if (Objects.isNull(this.payFee)) {
            return payFee;
        }
        if (Objects.isNull(payFee)) {
            return this.payFee;
        }
        return this.payFee + payFee;
    }

    @JsonIgnore
    public Long getRefundOriginAmountAdded(Long refundOriginAmount) {
        if (Objects.isNull(this.refundOriginAmount)) {
            return refundOriginAmount;
        }
        if (Objects.isNull(refundOriginAmount)) {
            return this.refundOriginAmount;
        }
        return this.refundOriginAmount + refundOriginAmount;
    }

    @JsonIgnore
    public Long getRefundFeeAdded(Long refundFee) {
        if (Objects.isNull(this.refundFee)) {
            return refundFee;
        }
        if (Objects.isNull(refundFee)) {
            return this.refundFee;
        }
        return this.refundFee + refundFee;
    }

    @JsonIgnore
    public Long calculatePaySharingAmount(SharingBasisEnum sharingBasis) {
        if (Objects.isNull(sharingBasis)) {
            // 默认按结算金额
            return payOriginAmount - (Objects.nonNull(payFee) ? payFee : 0L);
        }

        return switch (sharingBasis) {
            case SETTLEMENT_AMOUNT ->
                // 结算金额 = 原始金额 - 手续费
                    payOriginAmount - (Objects.nonNull(payFee) ? payFee : 0L);
            case ACTUAL_AMOUNT -> {
                // 实收金额 = 原始金额 - 优惠金额
                Long discountAmount = (Objects.nonNull(discount) && Objects.nonNull(discount.getAllDiscountAmount()))
                        ? discount.getAllDiscountAmount()
                        : 0L;
                yield payOriginAmount - discountAmount;
            }
            case ORDER_AMOUNT ->
                // 订单金额 = 原始金额
                    payOriginAmount;
        };
    }

    @JsonIgnore
    public Long getSettlementAmount() {
        if(Objects.isNull(fee)) {
            return originAmount;
        }
        return originAmount - fee;
    }

    public FundPoolAmountVO add(FundBillAmountVO amount) {
        if (Objects.isNull(amount)) {
            return this;
        }
        return toBuilder()
                .originAmount(getTotalAmountAdded(amount.getOriginAmount()))
                .fee(getFeeAdded(amount.getFee()))
                .build();
    }


    @JsonIgnore
    public Long getTotalAmountAdded( Long originAmount) {
        if (Objects.isNull(originAmount)) {
            return getOriginAmount();
        }
        return getOriginAmount() + originAmount;
    }

}
