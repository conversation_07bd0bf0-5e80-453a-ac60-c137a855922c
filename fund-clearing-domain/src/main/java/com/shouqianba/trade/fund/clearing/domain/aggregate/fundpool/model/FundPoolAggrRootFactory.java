package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolExtVO;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.wosai.general.util.validation.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
public class FundPoolAggrRootFactory extends BaseFactory {
    public static FundPoolAggrRootBuilder builder() {
        return new FundPoolAggrRootBuilder(FundPoolAggrRoot.newEmptyInstance());
    }

    public static class FundPoolAggrRootBuilder extends BaseBuilder<FundPoolAggrRoot, FundPoolAggrRootCoreBuilder, FundPoolAggrRootOptionalBuilder> {
        private FundPoolAggrRootCoreBuilder fundPoolAggrRootCoreBuilder;
        private FundPoolAggrRootOptionalBuilder fundPoolAggrRootOptionalBuilder;

        protected FundPoolAggrRootBuilder(FundPoolAggrRoot fundPoolAggrRoot) {
            super(fundPoolAggrRoot);
        }

        @Override
        public FundPoolAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(fundPoolAggrRootCoreBuilder)) {
                fundPoolAggrRootCoreBuilder = new FundPoolAggrRootCoreBuilder(aggrRoot);
            }
            return fundPoolAggrRootCoreBuilder;
        }

        @Override
        public FundPoolAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(fundPoolAggrRootOptionalBuilder)) {
                fundPoolAggrRootOptionalBuilder = new FundPoolAggrRootOptionalBuilder(aggrRoot);
            }
            return fundPoolAggrRootOptionalBuilder;
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT
                        , result.getMsg());
            }
        }
    }

    public static class FundPoolAggrRootCoreBuilder extends FundPoolAggrRootBuilder implements BaseCoreBuilder {

        protected FundPoolAggrRootCoreBuilder(FundPoolAggrRoot fundPoolAggrRoot) {
            super(fundPoolAggrRoot);
        }

        public FundPoolAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public FundPoolAggrRootCoreBuilder batchId(Long batchId) {
            aggrRoot.setBatchId(batchId);
            return this;
        }

        public FundPoolAggrRootCoreBuilder payeeType(AccountTypeEnum payeeType) {
            aggrRoot.setPayeeType(payeeType);
            return this;
        }

        public FundPoolAggrRootCoreBuilder brandSn(String brandSn) {
            aggrRoot.setBrandSn(brandSn);
            return this;
        }
        
        public FundPoolAggrRootCoreBuilder merchantSn(String merchantSn) {
            aggrRoot.setMerchantSn(merchantSn);
            return this;
        }
        
        public FundPoolAggrRootCoreBuilder storeSn(String storeSn) {
            aggrRoot.setStoreSn(storeSn);
            return this;
        }

        public FundPoolAggrRootCoreBuilder payeeInfo(PayeeInfoVO payeeInfo) {
            aggrRoot.setPayeeInfo(payeeInfo);
            return this;
        }

        public FundPoolAggrRootCoreBuilder billSource(BillSourceEnum billSource) {
            aggrRoot.setBillSource(billSource);
            return this;
        }

        public FundPoolAggrRootCoreBuilder acquiringCompany(Integer acquiringCompany) {
            aggrRoot.setAcquiringCompany(acquiringCompany);
            return this;
        }

        public FundPoolAggrRootCoreBuilder settleDate(LocalDate settleDate) {
            aggrRoot.setSettleDate(settleDate);
            return this;
        }

        public FundPoolAggrRootCoreBuilder amount(FundPoolAmountVO amount) {
            aggrRoot.setAmount(amount);
            return this;
        }

        public FundPoolAggrRootCoreBuilder bizDomain(FundPoolBizDomainVO bizDomain) {
            aggrRoot.setBizDomain(bizDomain);
            return this;
        }

        public FundPoolAggrRootCoreBuilder status(FundPoolStatusEnum status) {
            aggrRoot.setStatus(status);
            return this;
        }
    }

    public static class FundPoolAggrRootOptionalBuilder extends FundPoolAggrRootBuilder implements BaseOptionalBuilder {

        protected FundPoolAggrRootOptionalBuilder(FundPoolAggrRoot fundPoolAggrRoot) {
            super(fundPoolAggrRoot);
        }

        public FundPoolAggrRootOptionalBuilder ext(FundPoolExtVO ext) {
            aggrRoot.setExt(ext);
            return this;
        }

        public FundPoolAggrRootOptionalBuilder created(LocalDateTime created) {
            aggrRoot.setCreated(created);
            return this;
        }

        public FundPoolAggrRootOptionalBuilder updated(LocalDateTime updated) {
            aggrRoot.setUpdated(updated);
            return this;
        }

        public FundPoolAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }

        @Override
        public void initOptional() {
            FundPoolExtVO ext = aggrRoot.getExt();
            LocalDateTime created = aggrRoot.getCreated();
            LocalDateTime updated = aggrRoot.getUpdated();
            Long version = aggrRoot.getVersion();

            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(ext)) {
                aggrRoot.setExt(FundPoolExtVO.newEmptyInstance());
            }
            if (Objects.isNull(created)) {
                aggrRoot.setCreated(currentDateTime);
            }
            if (Objects.isNull(updated)) {
                aggrRoot.setUpdated(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
        }
    }
}
