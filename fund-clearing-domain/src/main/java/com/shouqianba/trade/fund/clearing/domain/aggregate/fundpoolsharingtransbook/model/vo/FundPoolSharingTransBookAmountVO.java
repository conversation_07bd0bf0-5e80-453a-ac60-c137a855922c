package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.vo;

import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class FundPoolSharingTransBookAmountVO extends BaseVO<FundPoolSharingTransBookAmountVO> {
    @NotNull(message = "原始金额不能为空")
    private Long originAmount;

    @NotNull(message = "手续费不能为空")
    private Long fee;

    @NotNull(message = "分账基础金额不能为空")
    private Long sharingBaseAmount;

    @NotNull(message = "分享金额不能为空")
    private Long sharingAmount;

    public static FundPoolSharingTransBookAmountVO newEmptyInstance() {
        return FundPoolSharingTransBookAmountVO.builder().build();
    }

    @Override
    protected FundPoolSharingTransBookAmountVO doReplaceNotNull(FundPoolSharingTransBookAmountVO vo) {
        FundPoolSharingTransBookAmountVO.FundPoolSharingTransBookAmountVOBuilder builder = toBuilder();
        return builder.build();
    }

}
