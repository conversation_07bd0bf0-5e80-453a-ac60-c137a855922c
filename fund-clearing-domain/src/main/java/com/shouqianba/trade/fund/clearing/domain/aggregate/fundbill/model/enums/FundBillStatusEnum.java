package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums;

import lombok.Getter;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
public enum FundBillStatusEnum {
    IN_CONFIRMATION((byte) 1, "待确认"),
    IN_ENTRY((byte) 2, "待入账"),
    ENTERED((byte) 3, "已入账"),
    ENTRY_SETTLED((byte) 4, "已入账结算"),
    ENTRY_TO_POOL((byte) 5, "已入资金池"),
    SHARING((byte) 6, "已分账"),
    SHARING_SETTLED((byte) 7, "已分账结算");

    public static final List<Byte> IS_SHARED_LIST = List.of(SHARING.getCode(), SHARING_SETTLED.getCode());
    public static final List<Byte> IS_SETTLED_LIST = List.of(ENTRY_SETTLED.getCode(), SHARING.getCode(), SHARING_SETTLED.getCode());
    public static final List<Byte> NOT_SETTLED_LIST = List.of(IN_CONFIRMATION.getCode(), IN_ENTRY.getCode(), ENTERED.getCode());


    private final byte code;
    private final String desc;

    FundBillStatusEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FundBillStatusEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        FundBillStatusEnum[] enums = FundBillStatusEnum.values();
        for (FundBillStatusEnum statusEnum : enums) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return null;
    }

    public static boolean isEntrySettled(FundBillStatusEnum status) {
        return status.getCode() == ENTRY_SETTLED.getCode();
    }

    public Object getCodeString() {
        return String.valueOf(code);
    }
}
