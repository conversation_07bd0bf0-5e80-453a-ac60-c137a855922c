package com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool;

import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query.FundPoolAggrQuery;

import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
public interface FundPoolDomainRepository {

    void save(FundPoolAggrRoot aggrRoot);

    void batchSave(List<FundPoolAggrRoot> aggrRoots);

    FundPoolAggrRoot query(FundPoolAggrQuery aggrQuery);

    List<FundPoolAggrRoot> batchQuery(FundPoolAggrQuery aggrQuery);

    Long count(FundPoolAggrQuery build);
}
