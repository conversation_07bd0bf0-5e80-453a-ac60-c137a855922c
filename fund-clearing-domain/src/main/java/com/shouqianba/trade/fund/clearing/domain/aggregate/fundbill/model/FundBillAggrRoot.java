package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseEntity;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillChannelEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.*;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.EntryStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.ReceiverVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.SharingRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SettleTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SharingBasisEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.PaywayEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class FundBillAggrRoot extends BaseEntity {

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private Long fundPoolId;
    private Long fundBatchId;

    @NotNull(message = "收单日期不能为空")
    private LocalDate acquiringDate;

    @NotNull(message = "账单日期不能为空")
    private LocalDate billDate;

    @NotNull(message = "收款方主体类型不能为空")
    private AccountTypeEnum payeeType;

    // 新增字段
    private String brandSn;
    private String merchantSn;
    private String storeSn;

    @Valid
    private PayeeInfoVO payeeInfo;

    @NotNull(message = "账单来源不能为空")
    private BillSourceEnum billSource;

    private FundBillChannelEnum channel;

    private FundBillTypeEnum type;

    @NotNull(message = "账单处理状态不能为空")
    private FundBillStatusEnum status;

    @Valid
    @NotNull(message = "金额不能为空")
    private FundBillAmountVO amount;

    private String transSn;

    private String orderSn;

    @Valid
    @NotNull(message = "交易领域信息不能为空")
    private FundBillTradeDomainVO tradeDomain;

    @Valid
    private FundBillBizDomainVO bizDomain;

    @Valid
    @NotNull(message = "扩展字段不能为空")
    private FundBillExtVO ext;
    
    protected FundBillAggrRoot() {
    }

    public static FundBillAggrRoot newEmptyInstance() {
        return new FundBillAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BILL_NOT_EXIST);
        }
    }

    public Long getRefundableAmount(SharingBasisEnum sharingBasis) {
        return this.amount.getRefundableAmount(sharingBasis);
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public boolean isInConfirm() {
        return Objects.equals(this.getStatus(),
                FundBillStatusEnum.IN_CONFIRMATION);
    }

    public LocalDateTime parseSettleBeginLocalTime() {
        return tradeDomain.parseSettleBeginLocalTime();
    }

    public LocalDateTime parseSettleEndLocalTime() {
        return tradeDomain.parseSettleEndLocalTime();
    }

    public FundPoolFlowTypeEnum getFundPoolFlowTypeEnum() {
        if (Objects.equals(this.getType(), FundBillTypeEnum.PAYMENT)) {
            return FundPoolFlowTypeEnum.INCOME;
        }
        if (Objects.equals(this.getType(), FundBillTypeEnum.REFUND)) {
            return FundPoolFlowTypeEnum.EXPENSE;
        }

        throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BILL_TYPE_NOT_SUPPORT_POOL_FLOW);
    }

    public Long getSettlementAmount() {
        return amount.getSettlementAmount();
    }

    public Long calculateSharingAmount(SharingBasisEnum sharingBasis) {
        return amount.calculateSharingAmount(sharingBasis) - amount.getPreRefundAmountNotNull();
    }

    public Long getOriginAmount() {
        return amount.getOriginAmount();
    }

    public Long getFee() {
        return amount.getFeeNullToZero();
    }

    public void updateTransferStatus(EntryStatusEnum transferStatus) {
        if (Objects.nonNull(transferStatus)) {
            this.bizDomain = bizDomain.replaceNotNull(FundBillBizDomainVO
                    .builder()
                    .transferStatus(transferStatus)
                    .build());
            this.modifyUpdated();
        }
    }

    public void updateStatus(FundBillStatusEnum status) {
        if (Objects.nonNull(status)) {
            this.status = status;
            this.modifyUpdated();
        }
    }

    public void updateStatusToSharing(SharingRuleAggrRoot ruleAggrRoot) {
        this.status = FundBillStatusEnum.SHARING;
        this.bizDomain = bizDomain.replaceNotNull(FundBillBizDomainVO
                .builder()
                .fundSharingRuleId(ruleAggrRoot.getId())
                .build());
        this.modifyUpdated();
    }

    public void updateFundPoolId(Long fundPoolId) {
        if (Objects.nonNull(fundPoolId)) {
            this.fundPoolId = fundPoolId;
            this.modifyUpdated();
        }
    }

    public void updateFundBatchId(Long fundBatchId) {
        if (Objects.nonNull(id)) {
            this.fundBatchId = fundBatchId;
            if (Objects.isNull(fundBatchId)) {
                this.updateFundBatchIdToNull = Boolean.TRUE;
            } else {
                this.updateFundBatchIdToNull = null;
            }
            this.modifyUpdated();
        }
    }

    /**
     * 检查是否有分账接收方信息
     *
     * @return true-有接收方信息，false-无接收方信息
     */
    public boolean hasReceivers() {
        return Optional
                .ofNullable(this.bizDomain)
                .map(FundBillBizDomainVO::getChannelBillInfoAsSqbChannelBillInfoVO)
                .map(SqbChannelBillInfoVO::getProfitSharing)
                .map(SqbChannelBillInfoVO.ChannelProfitSharingVO::getReceivers)
                .map(receivers -> !receivers.isEmpty())
                .orElse(false);
    }

    /**
     * 获取分账接收方列表
     *
     * @return 分账接收方列表
     */
    public List<SqbChannelBillInfoVO.ChannelReceiverVO> getReceivers() {

        return Optional
                .ofNullable(this.bizDomain)
                .map(FundBillBizDomainVO::getChannelBillInfoAsSqbChannelBillInfoVO)
                .map(SqbChannelBillInfoVO::getProfitSharing)
                .map(SqbChannelBillInfoVO.ChannelProfitSharingVO::getReceivers)
                .orElse(Collections.emptyList());
    }

    /**
     * 判断是否为延迟分账模式
     *
     * @return true-延迟分账模式，false-非延迟分账模式
     */
    public boolean isDelaySharing() {
        return Optional
                .ofNullable(this.bizDomain)
                .map(FundBillBizDomainVO::getChannelBillInfoAsSqbChannelBillInfoVO)
                .map(SqbChannelBillInfoVO::getProfitSharing)
                .map(SqbChannelBillInfoVO.ChannelProfitSharingVO::getDelayed)
                .map(delayed -> Objects.equals(delayed, Boolean.TRUE))
                .orElse(false);
    }

    public Long updateRefundAmountAndGetRefundAmount(Long shouldRefundAmount, SharingBasisEnum sharingBasisEnum) {
        // 获取当前已退金额
        Long currentRefundAmount = amount.getRefundedAmountDefaultZero();

        // 计算可退金额
        Long refundableAmount = amount.getRefundableAmount(sharingBasisEnum);

        if (refundableAmount <= 0) {
            return 0L; // 已无可退金额，返回全部应退金额
        }
        if (shouldRefundAmount > refundableAmount) {
            // 更新已退金额为已分金额
            amount = amount.replaceNotNull(FundBillAmountVO
                    .builder()
                    .refundAmount(amount.calculateSharingAmount(sharingBasisEnum))
                    .build());
            modifyUpdated();

            // 返回本订单可退金额
            return refundableAmount;
        } else {
            // 更新已退金额
            amount = amount.replaceNotNull(FundBillAmountVO
                    .builder()
                    .refundAmount(currentRefundAmount + shouldRefundAmount)
                    .build());
            return shouldRefundAmount;
        }
    }

    public Long updatePreRefundAmountAndGetRefundAmount(Long shouldRefundAmount, SharingBasisEnum sharingBasisEnum) {
        // 获取当前已退金额
        Long currentRefundAmount = amount.getRefundedAmountDefaultZero();

        // 计算可退金额
        Long refundableAmount = amount.getRefundableAmount(sharingBasisEnum);

        if (refundableAmount <= 0) {
            return 0L; // 已无可退金额，返回全部应退金额
        }
        if (shouldRefundAmount > refundableAmount) {
            // 更新已退金额为已分金额
            amount = amount.replaceNotNull(FundBillAmountVO
                    .builder()
                    .preRefundAmount(amount.calculateSharingAmount(sharingBasisEnum))
                    .build());
            modifyUpdated();

            // 返回本订单可退金额
            return refundableAmount;
        } else {
            // 更新已退金额
            amount = amount.replaceNotNull(FundBillAmountVO
                    .builder()
                    .preRefundAmount(currentRefundAmount + shouldRefundAmount)
                    .build());
            return shouldRefundAmount;
        }
    }

    /**
     * 生成退款接受方
     */
    public List<ReceiverVO> genRefundReceiver(
            List<ReceiverVO> receiverValues, Long shouldRefundAmount
            , SharingBasisEnum sharingBasisEnum, long transactionPayAmount) {
        if (CollectionUtils.isEmpty(receiverValues)) {
            return List.of();
        }

        long totalSharing = receiverValues
                .stream()
                .map(ReceiverVO::getSharingAmount)
                .reduce(0L, Long::sum);
        //计算该订单在整个交易中的占比
        Long sharingBaseAmount = calculateSharingAmount(sharingBasisEnum);
        double billRatio = (double) sharingBaseAmount / transactionPayAmount;
        //计算该订单退还比例
        double refundRatio = (double) shouldRefundAmount / sharingBaseAmount;
        totalSharing = (long) (totalSharing * billRatio * refundRatio);
        // 处理除最后一个接收方外的所有接收方
        List<ReceiverVO> result = new ArrayList<>();
        long currentTotalRefund = 0L;
        for (int i = 0; i < receiverValues.size() - 1; i++) {
            ReceiverVO receiver = receiverValues.get(i);
            long refundValue = (long) (receiver.getSharingAmount() * refundRatio * billRatio);
            currentTotalRefund += refundValue;
            result.add(ReceiverVO
                    .builder()
                    .id(receiver.getId())
                    .sharingAmount(refundValue)
                    .sharingName(receiver.getSharingName())
                    .sharingScene(receiver.getSharingScene())
                    .build());
        }

        // 处理最后一个接收方，分配剩余金额
        ReceiverVO lastReceiver = receiverValues.getLast();
        Long lastRefundValue = totalSharing - currentTotalRefund;

        result.add(ReceiverVO
                .builder()
                .id(lastReceiver.getId())
                .sharingAmount(lastRefundValue)
                .sharingName(lastReceiver.getSharingName())
                .sharingScene(lastReceiver.getSharingScene())
                .build());

        return result;
    }

    /**
     * 判断账单是否需要分账
     *
     * @return true-需要分账，false-不需要分账
     */
    public boolean isNeedSharing() {
        return Optional
                .ofNullable(this.bizDomain)
                .map(FundBillBizDomainVO::isNeedSharing)
                .orElse(true);
    }

    public boolean isStatusValid(Byte fundBillStatus) {
        if (Objects.isNull(fundBillStatus)) {
            return false;
        }
        return Objects.equals(fundBillStatus, this
                .getStatus()
                .getCode());
    }

    public void checkStatusValid(Byte fundBillStatus) {
        if (!isStatusValid(fundBillStatus)) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BILL_STATUS_NOT_VALID);
        }
    }

    /**
     * 更新分账退款状态
     */
    public void updateSharingRefundStatus(SharingActionStatusEnum sharingActionStatusEnum) {
        if (Objects.nonNull(this.bizDomain)) {
            this.bizDomain = this.bizDomain.replaceNotNull(FundBillBizDomainVO
                    .builder()
                    .sharingRefundStatus(sharingActionStatusEnum)
                    .build());
        }

    }

    public boolean isTransferred() {
        if (Objects.isNull(this.bizDomain) || Objects.isNull(this.bizDomain.getTransferStatus())) {
            return Boolean.FALSE;
        }
        return Objects.equals(this.bizDomain.getTransferStatus(), EntryStatusEnum.SUCCESS);

    }

    public boolean isNotTransferred() {
        return !isTransferred();
    }

    public boolean isRefunded() {
        if (Objects.isNull(this.bizDomain) || Objects.isNull(this.bizDomain.getSharingRefundStatus())) {
            return Boolean.FALSE;
        }
        return Objects.equals(this.bizDomain.getSharingRefundStatus(), SharingActionStatusEnum.SUCCESS);
    }

    public boolean isNotRefunded() {
        return !isRefunded();
    }

    public boolean isInPool() {
        return Objects.nonNull(this.getFundPoolId());
    }

    public Long getBehaviorTreeId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getEntryBehaviorTreeId();
    }

    public Long getConfigFundCollectRuleBelongId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getFundCollectRuleBelongId();
    }

    public Long getCollectionRuleId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getFundCollectRuleId();
    }

    public Long getSharingRuleId() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getFundSharingRuleId();
    }

    public boolean isRefundAmountValid(Long refundAmount, SharingBasisEnum sharingBasisEnum) {
        return this.amount.getRefundableAmount(sharingBasisEnum) >= refundAmount;
    }

    public Boolean getTransferWithoutFee() {
        return Optional
                .ofNullable(this.tradeDomain)
                .map(FundBillTradeDomainVO::getTransferWithoutFee)
                .orElse(null);
    }

    public String getConfigFundCollectRuleBelongIdStr() {
        Long configFundCollectRuleBelongId = getConfigFundCollectRuleBelongId();
        if (Objects.isNull(configFundCollectRuleBelongId)) {
            return null;
        }
        return String.valueOf(configFundCollectRuleBelongId);
    }

    public LocalDateTime getTradeTime() {
        return Optional
                .ofNullable(this.tradeDomain)
                .map(FundBillTradeDomainVO::getTradeDateTime)
                .orElse(null);
    }

    public String getTradeTimeStr() {
        LocalDateTime tradeTime = getTradeTime();
        if (Objects.isNull(tradeTime)) {
            return null;
        }
        return tradeTime.format(FORMATTER);
    }

    public String getDefaultChannelMerchantSn() {
        if (Objects.isNull(bizDomain)) {
            return null;
        }
        return bizDomain.getDefaultChannelMerchantSn();
    }

    public Byte getPayWayCode() {
        if (Objects.isNull(tradeDomain)) {
            return null;
        }
        return tradeDomain.getPayWayCode();
    }

    public String getChannelTransSn() {
        if (Objects.isNull(tradeDomain)) {
            return null;
        }
        return tradeDomain.getChannelTransSn();
    }

    public String getChannelOrderSn() {
        if (Objects.isNull(tradeDomain)) {
            return null;
        }
        return tradeDomain.getChannelOrderSn();
    }

    public void updateSettleTransSn(String settleTransSn) {
        if (Objects.nonNull(settleTransSn)) {
            this.tradeDomain = this.tradeDomain.replaceNotNull(FundBillTradeDomainVO
                    .builder()
                    .settleTransSn(settleTransSn)
                    .build());
        }
    }

    public Integer getAcquiringCompany() {
        if (Objects.isNull(tradeDomain)) {
            return null;
        }
        return tradeDomain.getAcquiringCompany();
    }

    public LocalDate genSettleDate() {
        return Optional
                .ofNullable(this.tradeDomain)
                .map(FundBillTradeDomainVO::parseSettleEndLocalTime)
                .map(LocalDateTime::toLocalDate)
                .orElse(null);
    }

    public LocalDate getSettleDate() {
        return Optional
                .ofNullable(this.tradeDomain)
                .map(FundBillTradeDomainVO::parseSettleEndLocalTime)
                .map(datetime->datetime.plusSeconds(1))
                .map(LocalDateTime::toLocalDate)
                .orElse(null);
    }

    public Byte getSettleTypeCode() {
        if (Objects.isNull(tradeDomain)) {
            return null;
        }
        return Objects.equals(getType(), FundBillTypeEnum.PAYMENT) ?
                SettleTypeEnum.RECEIPT.getCode() : SettleTypeEnum.REFUND.getCode();
    }

    public PaywayEnum getPayway() {
        return tradeDomain.getPayWay();
    }

    public Boolean getUpdateFundBatchIdToNull() {
        return updateFundBatchIdToNull;
    }

    public boolean isRefund() {
        return Objects.equals(this.getType(), FundBillTypeEnum.REFUND);
    }
}
