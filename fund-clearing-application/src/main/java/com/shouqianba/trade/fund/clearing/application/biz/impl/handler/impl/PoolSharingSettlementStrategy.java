package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.api.request.model.FundTransactionCursorModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundTransactionSortModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.PoolSharingSettlementEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.FundPoolSharingTransactionDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.enums.FundPoolSharingTransactionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.FundPoolSharingTransBookDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.FundPoolSharingTransBookAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.enums.FundPoolSharingTransBookStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SettleTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.FundTransactionTypeEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.feishu.FeishuClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.feishu.model.FeishuSendRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementFlowCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementFlowCreateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2024/8/07 Time: 10:07
 */
@Slf4j
@Component
public class PoolSharingSettlementStrategy extends AbstractBehaviorTreeStrategy<PoolSharingSettlementEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.POOL_SHARING_BOOK_SETTLEMENT;
    private static final Integer DEFAULT_THRESHOLD = 3;
    public static final byte SETTLE_FLOW_TYPE_SHARING = (byte) 2;

    @Resource
    protected FundPoolSharingTransBookDomainRepository fundPoolSharingTransBookDomainRepository;
    @Resource
    protected SettleClient settleClient;
    @Resource
    protected FeishuClient feishuClient;
    @Resource
    private FundPoolSharingTransactionDomainRepository transactionDomainRepository;
    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;
    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<PoolSharingSettlementEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(PoolSharingSettlementEventContext context) {
        return new BillPushToSettlementRunnable(context);
    }

    public class BillPushToSettlementRunnable extends IndependentEventStrategyRunnable {
        private final PoolSharingSettlementEventContext context;

        public BillPushToSettlementRunnable(PoolSharingSettlementEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(PoolSharingSettlementEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(PoolSharingSettlementEventContext context) throws Throwable {
                    FundPoolAggrRoot fundPoolAggrRoot = fundPoolDomainRepository.query(context.genFundPoolAggrQuery());
                    fundPoolAggrRoot.checkExist();
                    context.bindFundPoolAggrRoot(fundPoolAggrRoot);

                    // 分页查询参数
                    int pageSize = 50;
                    String lastId = null;

                    while (true) {
                        // 查询池中状态为PENDING_SETTLEMENT的Transaction
                        List<FundPoolSharingTransactionAggrRoot> transactions = transactionDomainRepository.batchQuery(
                                FundPoolSharingTransactionAggrQuery.builder()
                                        .fundPoolId(context.getEventAggrRoot().getAssociatedSnNum())
                                        .status(FundPoolSharingTransactionStatusEnum.PENDING_SETTLEMENT.getCode())
                                        .cursorField(FundTransactionCursorModel.FundTransactionCursorFieldEnum.ID.getInnerField())
                                        .endCursor(lastId)
                                        .sortField(FundTransactionSortModel.FundTransactionSortFieldEnum.ID.getInnerField())
                                        .isDesc(Boolean.TRUE)
                                        .build());

                        if (transactions.isEmpty()) {
                            break;
                        }

                        // 创建transactionId到聚合根的映射
                        Map<Long, FundPoolSharingTransactionAggrRoot> transactionMap = transactions.stream()
                                .collect(Collectors.toMap(FundPoolSharingTransactionAggrRoot::getId, Function.identity()));

                        // 获取transactionId列表
                        List<Long> transactionIds = new ArrayList<>(transactionMap.keySet());

                        // 通过transactionId查询book
                        List<FundPoolSharingTransBookAggrRoot> aggrRoots = fundPoolSharingTransBookDomainRepository.batchQuery(
                                context.genSharingBookAggrQuery(transactionIds));

                        // 处理查询到的book
                        for (FundPoolSharingTransBookAggrRoot aggrRoot : aggrRoots) {
                            processPush(context, aggrRoot, transactionMap.get(aggrRoot.getTransactionId()));
                        }

                        // 如果返回的记录数小于页大小，说明没有更多数据了
                        if (transactions.size() < pageSize) {
                            break;
                        }

                        // 更新游标，使用最后一条记录的ID
                        lastId = transactions.getLast().getIdStr();
                    }
                    updatePoolStatus(context, FundPoolStatusEnum.SETTLEMENT_COMPLETED);
                    context.bindBlackboardId(context.getBlackboardId());
                    context.bindBehaviorTreeId(context.getBehaviorTreeId());
                    executeBehaviorTreeAndProcessEventResult(context);
                }

                @Override
                protected void postInvokeExternal(PoolSharingSettlementEventContext PoolSharingSettlementEventContext) {
                }

                @Override
                protected void onBizFailure(PoolSharingSettlementEventContext context, FundClearingBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                }

                @Override
                protected void onFailure(PoolSharingSettlementEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                }

                @Override
                protected void doFinally(PoolSharingSettlementEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

    protected void processPush(PoolSharingSettlementEventContext context, FundPoolSharingTransBookAggrRoot aggrRoot
            , FundPoolSharingTransactionAggrRoot transaction) {
        FundClearingSettlementFlowCreateRequest.AccountModel payAccountModel = FundClearingSettlementFlowCreateRequest.AccountModel.builder()
                .type(aggrRoot.getPayeeType().getCode())
                .brandSn(aggrRoot.getPayeeInfo().getBrandSn())
                .merchantSn(aggrRoot.getPayeeInfo().getFundMerchantSn())
                .storeSn(aggrRoot.getPayeeInfo().getStoreSn())
                .build();
        FundClearingSettlementFlowCreateRequest.AccountModel receiveAccountModel = FundClearingSettlementFlowCreateRequest.AccountModel.builder()
                .type(aggrRoot.getReceiverType().getCode())
                .brandSn(aggrRoot.getReceiverInfo().getBrandSn())
                .merchantSn(aggrRoot.getReceiverInfo().getFundMerchantSn())
                .storeSn(aggrRoot.getReceiverInfo().getStoreSn())
                .build();
        FundClearingSettlementFlowCreateRequest.AmountModel amountModel;
        FundClearingSettlementFlowCreateRequest.BizInfoModel bizInfoModel;
        FundPoolAggrRoot fundPoolAggrRoot = context.getFundPoolAggrRoot();

        if (transaction.isBillTransaction()) {
            FundBillAggrRoot fundBillAggrRoot = fundBillDomainRepository.query(FundBillAggrQuery.builder()
                    .id(transaction.getFundBillId())
                    .status(FundBillStatusEnum.SHARING.getCode())
                    .build());
            amountModel = FundClearingSettlementFlowCreateRequest.AmountModel.builder()
                    .originAmount(fundBillAggrRoot.getAmount().getOriginAmount())
                    .fee(fundBillAggrRoot.getAmount().getFee())
                    .settleAmount(aggrRoot.getSharingAmount())
                    .build();
            bizInfoModel = FundClearingSettlementFlowCreateRequest.BizInfoModel.builder().build();
        } else {
            amountModel = FundClearingSettlementFlowCreateRequest.AmountModel.builder()
                    .originAmount(fundPoolAggrRoot.getAmount().getOriginAmount())
                    .fee(fundPoolAggrRoot.getAmount().getFee())
                    .settleAmount(aggrRoot.getSharingAmount())
                    .build();
            bizInfoModel = FundClearingSettlementFlowCreateRequest.BizInfoModel.builder().build();
        }

        FundBatchAggrRoot fundBatchAggrRoot =
                fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
        fundBatchAggrRoot.checkExist();

        FundClearingSettlementFlowCreateRequest request = FundClearingSettlementFlowCreateRequest.builder()
                .type(SETTLE_FLOW_TYPE_SHARING)
                .batchId(fundBatchAggrRoot.getSettleBatchId())
                .poolId(transaction.getFundPoolId())
                .transSn(transaction.getIdStr())
                .orderSn(transaction.getIdStr())
                .fromInfo(transaction.isPayTransaction() ? payAccountModel : receiveAccountModel)
                .toInfo(transaction.isPayTransaction() ? receiveAccountModel : payAccountModel)
                .amount(amountModel)
                .tradeInfo(FundClearingSettlementFlowCreateRequest.TradeInfoModel.builder()
                        .tradeTime(fundPoolAggrRoot.getDefaultFormatCtime())
                        .flowChannel(fundPoolAggrRoot.getBillSource().getCode())
                        .acquiringCompany(fundPoolAggrRoot.getAcquiringCompany())
                        .payWay(fundPoolAggrRoot.getPayway().getCode())
                        .settleType(Objects.equals(transaction.getType(), FundTransactionTypeEnum.PAYMENT) ?
                                SettleTypeEnum.SHARING.getCode() : SettleTypeEnum.SHARING_RETURN.getCode())
                        .build())
                .bizInfo(bizInfoModel)
                .build();

        for (int i = 0; i < DEFAULT_THRESHOLD; i++) {
            FundClearingSettlementFlowCreateResult result = settleClient.createSettlementFlow(request);
            if (result.isSuccess()) {
                updateBookStatus(aggrRoot, FundPoolSharingTransBookStatusEnum.PROCESSED);
                return;
            }
        }
        updateBookStatus(aggrRoot, FundPoolSharingTransBookStatusEnum.FAILED);
        sendMsgToFeishu(context);
    }

    protected void updateBookStatus(FundPoolSharingTransBookAggrRoot aggrRoot, FundPoolSharingTransBookStatusEnum statusEnum) {
        aggrRoot.updateStatus(statusEnum);
        fundPoolSharingTransBookDomainRepository.save(aggrRoot);
    }

    protected void updatePoolStatus(PoolSharingSettlementEventContext context, FundPoolStatusEnum statusEnum) {
        FundPoolAggrRoot aggrRoot = context.getFundPoolAggrRoot();
        aggrRoot.updateStatus(statusEnum);
        fundPoolDomainRepository.save(aggrRoot);
    }

    protected void sendMsgToFeishu(PoolSharingSettlementEventContext context) {
        try {
            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
            FeishuSendRequest request = FeishuSendRequest
                    .genEventProcessAlarmRequest(eventAggrRoot.getType().getDesc()
                            , eventAggrRoot.getId()
                            , DEFAULT_THRESHOLD
                            , DEFAULT_THRESHOLD);
            feishuClient.sendNotice(request);
        } catch (Throwable ignored) {
        }
    }

}