package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseIndependentEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundChannelNotifyEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.EventDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.BrandBusinessClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.wosai.pantheon.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundChannelNotifyStrategy extends BaseIndependentEventStrategy<FundChannelNotifyEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_CHANNEL_NOTIFY;

    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private BrandBusinessClient brandBusinessClient;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundChannelNotifyEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundChannelNotifyEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundChannelNotifyEventContext context;

        public FundBillEntryRunnable(FundChannelNotifyEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundChannelNotifyEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundChannelNotifyEventContext context) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();

                            BrandDetailInfoGetResult brandDetailInfo =
                                    brandBusinessClient.getBrandDetailInfo(context.genBrandDetailInfoGetRequest());
                            brandDetailInfo.checkExist();
                            context.bindBrandDetailInfoGetResult(brandDetailInfo);

                            // 查询批次
                            List<FundBatchAggrRoot> fundBatchAggrRoots =
                                    fundBatchDomainRepository.batchQuery(context.genFundBatchAggrQuery());
                            if (CollectionUtil.isEmpty(fundBatchAggrRoots)) {
                                throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BATCH_NOT_EXIST);
                            }
                            FundBatchAggrRoot fundBatchAggrRoot = fundBatchAggrRoots.getFirst();
                            context.bindFundBatchAggrRoot(fundBatchAggrRoot);

                            // 动账通知金额大于等于账单汇总金额，使用账单汇总金额清分
                            if (context.isFullClearingNotify()) {
                                eventDomainRepository.save(context.genFullClearingEvent());
                                aggrRoot.processSuccess("动账通知全部清分事件触发成功");
                                return;
                            }

                            // 动账通知金额小于账单汇总金额，使用动账通知金额入账，从最后剔除已入账金额
                            if (context.isPartialClearingNotify()) {
                                eventDomainRepository.save(context.genPartialClearingEvent());
                                aggrRoot.processSuccess("动账通知部分清分事件触发成功");
                                return;
                            }

                            aggrRoot.processFailure("未知动账通知类型");

                        }

                        @Override
                        protected void postInvokeExternal(FundChannelNotifyEventContext FundChannelNotifyEventContext) {
                        }

                        @Override
                        protected void onBizFailure(FundChannelNotifyEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundChannelNotifyEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundChannelNotifyEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
