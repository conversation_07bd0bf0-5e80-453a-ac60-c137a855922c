package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.api.request.model.FundBillCursorModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundBillSortModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.GenBillPayTransactionEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentGenBillPayTransactionVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.FundPoolSharingTransactionDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.enums.FundPoolSharingTransactionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.FundPoolSharingTransactionAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.FundPoolSharingTransactionBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.SharingRuleDomainService;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.SharingRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SharingBaseEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.FundTransactionTypeEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GenBillPayTransactionStrategy extends AbstractBehaviorTreeStrategy<GenBillPayTransactionEventContext> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.GEN_ORDER_FORWARD_TRANSACTION;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundPoolSharingTransactionDomainRepository transactionDomainRepository;
    @Resource
    private SharingRuleDomainService sharingRuleDomainService;
    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;


    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<GenBillPayTransactionEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(GenBillPayTransactionEventContext context) {
        return new GenBillPayTransactionRunnable(context);
    }

    public class GenBillPayTransactionRunnable extends IndependentEventStrategyRunnable {
        private final GenBillPayTransactionEventContext context;

        public GenBillPayTransactionRunnable(GenBillPayTransactionEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(GenBillPayTransactionEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(GenBillPayTransactionEventContext context) throws Throwable {
                    EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
                    FundPoolAggrRoot fundPoolAggrRoot = fundPoolDomainRepository.query(context.genFundPoolAggrQuery());
                    fundPoolAggrRoot.checkExist();
                    context.bindFundPoolAggrRoot(fundPoolAggrRoot);

                    SharingRuleAggrRoot sharingRuleAggrRoot = sharingRuleDomainService.querySharingRuleById(context.getSharingRuleId());
                    context.bindSharingRuleAggrRoot(sharingRuleAggrRoot);

                    int pageSize = 100;
                    String lastId = context.getLastId();
                    try {
                        while (true) {
                            List<FundBillAggrRoot> fundBills = fundBillDomainRepository.batchQuery(
                                    FundBillAggrQuery.builder()
                                            .poolId(context.getPoolId())
                                            .type(FundBillTypeEnum.PAYMENT.getCode())
                                            .status(FundBillStatusEnum.ENTRY_SETTLED.getCode())
                                            .querySize(pageSize)
                                            .cursorField(FundBillCursorModel.FundBillCursorFieldEnum.ID.getInnerField())
                                            .endCursor(lastId)
                                            .sortField(FundBillSortModel.FundBillSortFieldEnum.ID.getInnerField())
                                            .isDesc(Boolean.TRUE)
                                            .build()
                            );
                            genSharingTransaction(fundBills, context);
                            if (fundBills.size() < pageSize) {
                                break;
                            }
                            lastId = fundBills.getLast().getId().toString();
                        }
                    } catch (Exception e) {
                        eventAggrRoot.updateContent(EventContentVO.builder()
                                .bizContent(EventContentGenBillPayTransactionVO.builder()
                                        .poolId(context.getPoolId())
                                        .sharingRuleId(context.getSharingRuleId())
                                        .lastId(lastId)
                                        .build().toJsonString())
                                .build());
                        throw e;
                    }
                }

                @Override
                protected void postInvokeExternal(GenBillPayTransactionEventContext GenBillPayTransactionEventContext) {
                    updateFundPoolActionStatus(context, SharingActionStatusEnum.SUCCESS);
                    context.bindBlackboardId(context.getBlackboardId());
                    context.bindBehaviorTreeId(context.getBehaviorTreeId());
                    executeBehaviorTreeAndProcessEventResult(context);
                }

                @Override
                protected void onBizFailure(GenBillPayTransactionEventContext context, FundClearingBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                    if (aggrRoot.isExceededProcessingLimit()) {
                        updateFundPoolActionStatus(context, SharingActionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void onFailure(GenBillPayTransactionEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                    if (aggrRoot.isExceededProcessingLimit()) {
                        updateFundPoolActionStatus(context, SharingActionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void doFinally(GenBillPayTransactionEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

    private void updateFundPoolActionStatus(GenBillPayTransactionEventContext context, SharingActionStatusEnum sharingActionStatusEnum) {
        FundPoolAggrRoot fundPoolAggrRoot = context.getFundPoolAggrRoot();
        fundPoolAggrRoot.updateSharingActionStatus(FundPoolBizDomainVO.SharingActionStatusVO.builder()
                .genBillPayTransactionStatus(sharingActionStatusEnum)
                .build());
        fundPoolDomainRepository.save(fundPoolAggrRoot);
    }


    public void genSharingTransaction(List<FundBillAggrRoot> bills, GenBillPayTransactionEventContext context) {
        SharingRuleAggrRoot sharingRuleAggrRoot = context.getSharingRuleAggrRoot();
        List<FundPoolSharingTransactionAggrRoot> fundPoolSharingTransactionAggrRoots = bills.stream()
                .filter(FundBillAggrRoot::isNeedSharing)
                .map(bill -> FundPoolSharingTransactionAggrRootFactory.builder()
                        .coreBuilder()
                        .id(DefaultSerialGenerator.getInstance().genFundPoolSharingTransactionId())
                        .type(FundTransactionTypeEnum.ofCode(bill.getType().getCode()))
                        .sharingType(SharingBaseEnum.ORDER_AMOUNT)
                        .fundPoolId(bill.getFundPoolId())
                        .fundBillId(bill.getId())
                        .payeeType(bill.getPayeeType())
                        .brandSn(bill.getBrandSn())
                        .merchantSn(bill.getMerchantSn())
                        .storeSn(bill.getStoreSn())
                        .payeeInfo(bill.getPayeeInfo())
                        .amount(FundPoolSharingTransactionAmountVO.builder()
                                .originAmount(bill.getOriginAmount())
                                .fee(bill.getFee())
                                .sharingBaseAmount(bill.calculateSharingAmount(sharingRuleAggrRoot.getSharingBasis()))
                                .build())
                        .bizDomain(FundPoolSharingTransactionBizDomainVO.builder()
                                .fundSource(BillSourceEnum.ofCode(bill.getBillSource().getCode()))
                                .fundSharingRuleId(sharingRuleAggrRoot.getId())
                                .build())
                        .status(FundPoolSharingTransactionStatusEnum.PENDING)
                        .optionalBuilder()
                        .build())
                .toList();
        transactionDomainRepository.batchSave(fundPoolSharingTransactionAggrRoots);
    }

}
