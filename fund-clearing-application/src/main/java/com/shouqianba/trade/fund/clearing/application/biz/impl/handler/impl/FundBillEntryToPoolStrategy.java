package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBillEntryToPoolEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.infrastructure.service.FundPoolSupportService;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBillEntryToPoolStrategy extends AbstractBehaviorTreeStrategy<FundBillEntryToPoolEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_BILL_ENTRY_TO_POOL;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;
    @Resource
    private FundPoolSupportService fundPoolSupportService;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBillEntryToPoolEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundBillEntryToPoolEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBillEntryToPoolEventContext context;

        public FundBillEntryRunnable(FundBillEntryToPoolEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBillEntryToPoolEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBillEntryToPoolEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
                            // 1. 查询资金账单
                            FundBillAggrRoot fundBillAggrRoot =
                                    fundBillDomainRepository.query(context.genFundBillAggrQuery());
                            fundBillAggrRoot.checkExist();
                            context.bindFundBillAggrRoot(fundBillAggrRoot);

                            // 查询资金池
                            FundPoolAggrRoot fundPoolAggrRoot = fundPoolSupportService.queryFundPoolByBill(fundBillAggrRoot);
                            if (fundPoolAggrRoot.isExist()) {
                                if (fundPoolAggrRoot.isEntrySettled()) {
                                    log.info("[执行资金池入账功能]已进入结算极端，无法再入账，请检查，fundBillId=[{}], fundPoolId=[{}]",
                                            fundBillAggrRoot.getId(), fundPoolAggrRoot.getId());
                                   eventAggrRoot.processFailure("已进入结算极端，无法再入账，请检查");
                                   return;
                                }
                            }else{
                                // 创建资金池
                                fundPoolAggrRoot = FundPoolAggrRootFactory
                                        .builder()
                                        .coreBuilder()
                                        .id(DefaultSerialGenerator
                                                .getInstance()
                                                .genFundPoolId())
                                        .batchId(fundBillAggrRoot.getFundBatchId())
                                        .payeeType(fundBillAggrRoot.getPayeeType())
                                        .brandSn(fundBillAggrRoot.getBrandSn())
                                        .merchantSn(fundBillAggrRoot.getMerchantSn())
                                        .storeSn(fundBillAggrRoot.getStoreSn())
                                        .payeeInfo(fundBillAggrRoot.getPayeeInfo())
                                        .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                                        .billSource(fundBillAggrRoot.getBillSource())
                                        .settleDate(fundBillAggrRoot.getSettleDate())
                                        .amount(FundPoolAmountVO.newEmptyInstance())
                                        .bizDomain(FundPoolBizDomainVO
                                                .builder()
                                                .billEntrySettleBehaviorTreeId(1L) // todo 去掉
                                                .entryActionStatus(FundPoolBizDomainVO.EntryActionStatusVO.newEmptyInstance())
                                                .sharingActionStatus(FundPoolBizDomainVO.SharingActionStatusVO
                                                        .builder()
                                                        .build())
                                                .fundCollectRuleBelongId(fundBillAggrRoot
                                                        .getBizDomain()
                                                        .getFundCollectRuleBelongId())
                                                .billEntrySettleBehaviorTreeId(1L) // todo 去掉
                                                .entryActionStatus(FundPoolBizDomainVO.EntryActionStatusVO.newEmptyInstance())
                                                .payway(fundBillAggrRoot.getPayway())
                                                .build())
                                        .status(FundPoolStatusEnum.ENTRY_IN_PROGRESS)
                                        .build();
                                try {
                                    fundPoolDomainRepository.save(fundPoolAggrRoot);
                                } catch (DuplicateKeyException e) {
                                    fundPoolAggrRoot =
                                            computeFundPoolAggrRootByBill(fundBillAggrRoot.getId());
                                    fundPoolAggrRoot.checkExist();
                                }
                            }

                            fundPoolAggrRoot.updateAmount(fundPoolAggrRoot
                                    .getAmount()
                                    .add(fundBillAggrRoot.getAmount()));
                            fundPoolDomainRepository.save(fundPoolAggrRoot);
                            fundBillAggrRoot.updateFundPoolId(fundPoolAggrRoot.getId());
                            fundPoolDomainRepository.save(fundPoolAggrRoot);

                            eventAggrRoot.processSuccess("资金账单入账池次成功");

                        }

                        @Override
                        protected void postInvokeExternal(
                                FundBillEntryToPoolEventContext FundBillEntryToPoolEventContext) {
                        }

                        @Override
                        protected void onBizFailure(FundBillEntryToPoolEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundBillEntryToPoolEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBillEntryToPoolEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
    protected FundPoolAggrRoot computeFundPoolAggrRootByBill(Long fundBillId) {
        return fundPoolSupportService.queryFundPoolByBill(getFundBillAggrRootNotNull(fundBillId));
    }

    protected FundBillAggrRoot getFundBillAggrRootNotNull(
            Long fundBillAggrRootId) {
        FundBillAggrRoot fundBillAggrRoot = fundBillDomainRepository.query(FundBillAggrQuery
                .builder()
                .id(fundBillAggrRootId)
                .build());
        fundBillAggrRoot.checkExist();
        return fundBillAggrRoot;
    }
}
