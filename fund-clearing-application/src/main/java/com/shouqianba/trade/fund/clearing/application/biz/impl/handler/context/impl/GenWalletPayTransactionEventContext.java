package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.AbstractBehaviorTreeContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentGenWalletPayTransactionVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.query.FundPoolAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.SharingRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public class GenWalletPayTransactionEventContext extends AbstractBehaviorTreeContext<EventContentGenWalletPayTransactionVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.GEN_WALLET_FORWARD_TRANSACTION;

    private EventContentGenWalletPayTransactionVO eventContent;
    private FundPoolAggrRoot fundPoolAggrRoot;
    private SharingRuleAggrRoot sharingRuleAggrRoot;
    private Long totalFee;
    private Long totalOriginAmount;
    private Long totalSharingBaseAmount;

    static {
        registerContext(EVENT_TYPE, new GenWalletPayTransactionEventContext());
    }

    private GenWalletPayTransactionEventContext() {
    }

    private GenWalletPayTransactionEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public ClearingCustomBlackboard genClearingCustomBlackboard() {
        ClearingCustomBlackboard blackboard = new ClearingCustomBlackboard.ClearingCustomBlackboardBuilder().build();
        blackboard.setFundPoolAggrRootId(fundPoolAggrRoot.getIdStr());
        blackboard.setSharingRuleAggrRootId(sharingRuleAggrRoot.getIdStr());
        return blackboard;
    }

    @Override
    public EventStrategyContext<EventContentGenWalletPayTransactionVO> rebuildContext(EventHandlerContext context) {
        return new GenWalletPayTransactionEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentGenWalletPayTransactionVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot().getContent().getBizContent(),
                    EventContentGenWalletPayTransactionVO.class);
        }
        return eventContent;
    }

    public FundPoolAggrQuery genFundPoolAggrQuery() {
        return FundPoolAggrQuery.builder()
                .id(getPoolId())
                .status(FundPoolStatusEnum.ENTRY_BATCH_SETTLEMENT_SUCCEEDED)
                .build();
    }

    public Long getPoolId() {
        return this.getBizContent().getPoolId();
    }

    public Long getSharingRuleId() {
        return this.getBizContent().getSharingRuleId();
    }

    public Long getBlackboardId() {
        return this.getBizContent().getBlackBoardId();
    }

    public Long getBehaviorTreeId() {
        return this.getBizContent().getBehaviorTreeId();
    }

    public String getLastId() {
        return this.getBizContent().getLastId();
    }


    public void bindFundPoolAggrRoot(FundPoolAggrRoot fundPoolAggrRoot) {
        this.fundPoolAggrRoot = fundPoolAggrRoot;
    }

    public void bindSharingRuleAggrRoot(SharingRuleAggrRoot sharingRuleAggrRoot) {
        this.sharingRuleAggrRoot = sharingRuleAggrRoot;
    }

    public void bindTotalFee(long totalFee) {
        this.totalFee = totalFee;
    }

    public void bindTotalOriginAmount(long totalOriginAmount) {
        this.totalOriginAmount = totalOriginAmount;
    }

    public void bindTotalSharingBaseAmount(long totalSharingBaseAmount) {
        this.totalSharingBaseAmount = totalSharingBaseAmount;
    }
}
