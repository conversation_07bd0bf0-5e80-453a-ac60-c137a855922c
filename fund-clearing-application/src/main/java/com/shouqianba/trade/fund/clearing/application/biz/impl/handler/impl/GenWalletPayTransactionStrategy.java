package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.api.request.model.FundBillCursorModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundBillSortModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.GenWalletPayTransactionEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentGenWalletPayTransactionVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.FundPoolSharingTransactionDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.enums.FundPoolSharingTransactionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.FundPoolSharingTransactionAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.FundPoolSharingTransactionBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.ReceiverDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.model.ReceiverAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.model.ReceiverAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.SharingRuleDomainService;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.SharingRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SharingBaseEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.FundTransactionTypeEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GenWalletPayTransactionStrategy extends AbstractBehaviorTreeStrategy<GenWalletPayTransactionEventContext> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.GEN_WALLET_FORWARD_TRANSACTION;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundPoolSharingTransactionDomainRepository transactionDomainRepository;
    @Resource
    private ReceiverDomainRepository receiverDomainRepository;
    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;
    @Resource
    private SharingRuleDomainService sharingRuleDomainService;


    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<GenWalletPayTransactionEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(GenWalletPayTransactionEventContext context) {
        return new GenWalletPayTransactionRunnable(context);
    }

    public class GenWalletPayTransactionRunnable extends IndependentEventStrategyRunnable {
        private final GenWalletPayTransactionEventContext context;

        public GenWalletPayTransactionRunnable(GenWalletPayTransactionEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(GenWalletPayTransactionEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(GenWalletPayTransactionEventContext context) throws Throwable {
                    FundPoolAggrRoot fundPoolAggrRoot = fundPoolDomainRepository.query(context.genFundPoolAggrQuery());
                    fundPoolAggrRoot.checkExist();
                    context.bindFundPoolAggrRoot(fundPoolAggrRoot);

                    SharingRuleAggrRoot sharingRuleAggrRoot = sharingRuleDomainService.querySharingRuleById(context.getSharingRuleId());
                    context.bindSharingRuleAggrRoot(sharingRuleAggrRoot);

                    calculateTotalAmountAndUpdateStatus(context);

                    List<ReceiverAggrRoot> receivers = receiverDomainRepository.batchQuery(ReceiverAggrQuery.builder()
                            .ruleId(sharingRuleAggrRoot.getId())
                            .build());
                    FundPoolSharingTransactionAggrRoot transaction = FundPoolSharingTransactionAggrRootFactory.builder()
                            .coreBuilder()
                            .id(DefaultSerialGenerator.getInstance().genFundPoolSharingTransactionId())
                            .fundPoolId(fundPoolAggrRoot.getId())
                            .fundBillId(fundPoolAggrRoot.getId())
                            .payeeType(fundPoolAggrRoot.getPayeeType())
                            .brandSn(fundPoolAggrRoot.getBrandSn())
                            .merchantSn(fundPoolAggrRoot.getMerchantSn())
                            .storeSn(fundPoolAggrRoot.getStoreSn())
                            .type(FundTransactionTypeEnum.PAYMENT)
                            .sharingType(SharingBaseEnum.FUND_POOL_AMOUNT)
                            .payeeInfo(fundPoolAggrRoot.getPayeeInfo())
                            .amount(FundPoolSharingTransactionAmountVO.builder()
                                    .originAmount(context.getTotalOriginAmount())
                                    .fee(context.getTotalFee())
                                    .sharingBaseAmount(context.getTotalSharingBaseAmount())
                                    .build())
                            .bizDomain(FundPoolSharingTransactionBizDomainVO.builder()
                                    .fundSource(fundPoolAggrRoot.getBillSource())
                                    .fundSharingRuleId(fundPoolAggrRoot.getId())
                                    .build())
                            .status(FundPoolSharingTransactionStatusEnum.PENDING)
                            .build();
                    if (CollectionUtils.isNotEmpty(receivers)) {
                        transaction.updateReceiversByAggrRoot(sharingRuleAggrRoot, receivers, context.getTotalSharingBaseAmount());
                    }
                    transactionDomainRepository.save(transaction);
                }

                @Override
                protected void postInvokeExternal(GenWalletPayTransactionEventContext GenWalletPayTransactionEventContext) {
                    updateFundPoolActionStatus(context, SharingActionStatusEnum.SUCCESS);
                    context.bindBlackboardId(context.getBlackboardId());
                    context.bindBehaviorTreeId(context.getBehaviorTreeId());
                    executeBehaviorTreeAndProcessEventResult(context);
                }

                @Override
                protected void onBizFailure(GenWalletPayTransactionEventContext context, FundClearingBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                    if (aggrRoot.isExceededProcessingLimit()) {
                        updateFundPoolActionStatus(context, SharingActionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void onFailure(GenWalletPayTransactionEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                    if (aggrRoot.isExceededProcessingLimit()) {
                        updateFundPoolActionStatus(context, SharingActionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void doFinally(GenWalletPayTransactionEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

    private void updateFundPoolActionStatus(GenWalletPayTransactionEventContext context, SharingActionStatusEnum sharingActionStatusEnum) {
        FundPoolAggrRoot fundPoolAggrRoot = fundPoolDomainRepository.query(context.genFundPoolAggrQuery());
        fundPoolAggrRoot.checkExist();
        fundPoolAggrRoot.updateSharingActionStatus(FundPoolBizDomainVO.SharingActionStatusVO.builder()
                .genWalletPayTransactionStatus(sharingActionStatusEnum)
                .build());
        fundPoolDomainRepository.save(fundPoolAggrRoot);
    }

    private void calculateTotalAmountAndUpdateStatus(GenWalletPayTransactionEventContext context) {
        EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
        SharingRuleAggrRoot rule = context.getSharingRuleAggrRoot();

        int pageSize = 1000;
        String lastId = context.getLastId();
        long totalFee = 0L;
        long totalOriginAmount = 0L;
        long totalSharingBaseAmount = 0L;
        try {
            while (true) {
                List<FundBillAggrRoot> fundBills = fundBillDomainRepository.batchQuery(
                        FundBillAggrQuery.builder()
                                .poolId(context.getPoolId())
                                .type(FundBillTypeEnum.PAYMENT.getCode())
                                .status(FundBillStatusEnum.ENTRY_SETTLED.getCode())
                                .querySize(pageSize)
                                .cursorField(FundBillCursorModel.FundBillCursorFieldEnum.ID.getInnerField())
                                .endCursor(lastId)
                                .sortField(FundBillSortModel.FundBillSortFieldEnum.ID.getInnerField())
                                .isDesc(Boolean.TRUE)
                                .build());

                // 计算当前批次的总金额
                for (FundBillAggrRoot fundBill : fundBills) {
                    totalFee+= fundBill.getFee();
                    totalOriginAmount+= fundBill.getOriginAmount();
                    totalSharingBaseAmount += fundBill.calculateSharingAmount(rule.getSharingBasis());
                    fundBill.updateStatusToSharing(rule);
                }
                fundBillDomainRepository.batchSave(fundBills);
                // 如果本次查询的数量小于页大小，说明已经是最后一页
                if (fundBills.size() < pageSize) {
                    break;
                }
                lastId = fundBills.getLast().getId().toString();
            }
        } catch (Exception e) {
            eventAggrRoot.updateContent(EventContentVO.builder()
                    .bizContent(EventContentGenWalletPayTransactionVO.builder()
                            .poolId(context.getPoolId())
                            .sharingRuleId(context.getSharingRuleId())
                            .lastId(lastId)
                            .build().toJsonString())
                    .build());
            throw e;
        }
        context.bindTotalFee(totalFee);
        context.bindTotalOriginAmount(totalOriginAmount);
        context.bindTotalSharingBaseAmount(totalSharingBaseAmount);
    }

}
