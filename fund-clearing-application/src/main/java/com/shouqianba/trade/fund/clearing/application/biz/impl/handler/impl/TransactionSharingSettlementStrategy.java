package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.TransactionSharingSettlementEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.FundPoolSharingTransactionDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.enums.FundPoolSharingTransactionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.FundPoolSharingTransBookDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.FundPoolSharingTransBookAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.enums.FundPoolSharingTransBookStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SettleTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.FundTransactionTypeEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.feishu.FeishuClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.feishu.model.FeishuSendRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementFlowCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementFlowCreateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransactionSharingSettlementStrategy extends AbstractBehaviorTreeStrategy<TransactionSharingSettlementEventContext> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.TRANSACTION_SHARING_BOOK_SETTLEMENT;
    private static final Integer DEFAULT_THRESHOLD = 3;
    public static final byte SETTLE_FLOW_TYPE_SHARING = (byte) 2;

    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;
    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundPoolSharingTransBookDomainRepository fundPoolSharingTransBookDomainRepository;
    @Resource
    private FundPoolSharingTransactionDomainRepository fundPoolSharingTransactionDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    protected SettleClient settleClient;
    @Resource
    protected FeishuClient feishuClient;


    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<TransactionSharingSettlementEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(TransactionSharingSettlementEventContext context) {
        return new GenSingleRefundTransactionRunnable(context);
    }

    public class GenSingleRefundTransactionRunnable extends IndependentEventStrategyRunnable {
        private final TransactionSharingSettlementEventContext context;

        public GenSingleRefundTransactionRunnable(TransactionSharingSettlementEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(TransactionSharingSettlementEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(TransactionSharingSettlementEventContext context) throws Throwable {
                    FundPoolSharingTransactionAggrRoot fundPoolSharingTransactionAggrRoot =
                            fundPoolSharingTransactionDomainRepository.query(context.genFundPoolSharingTransactionAggrQuery());
                    fundPoolSharingTransactionAggrRoot.checkExist();
                    context.bindFundPoolSharingTransactionAggrRoot(fundPoolSharingTransactionAggrRoot);

                    FundPoolAggrRoot fundPoolAggrRoot = fundPoolDomainRepository.query(context.genFundPoolAggrQuery());
                    fundPoolAggrRoot.checkExist();
                    context.bindFundPoolAggrRoot(fundPoolAggrRoot);

                    // 通过transactionId查询book
                    List<FundPoolSharingTransBookAggrRoot> aggrRoots = fundPoolSharingTransBookDomainRepository.batchQuery(
                            context.genSharingBookAggrQuery(List.of(fundPoolSharingTransactionAggrRoot.getId())));

                    // 处理查询到的book
                    for (FundPoolSharingTransBookAggrRoot aggrRoot : aggrRoots) {
                        processPush(context, aggrRoot, fundPoolSharingTransactionAggrRoot);
                    }
                    updateTransactionStatus(fundPoolSharingTransactionAggrRoot, FundPoolSharingTransactionStatusEnum.PROCESSED);
                    context.bindBlackboardId(context.getBlackboardId());
                    context.bindBehaviorTreeId(context.getBehaviorTreeId());
                    executeBehaviorTreeAndProcessEventResult(context);
                }

                @Override
                protected void postInvokeExternal(TransactionSharingSettlementEventContext TransactionSharingSettlementEventContext) {
                }

                @Override
                protected void onBizFailure(TransactionSharingSettlementEventContext context, FundClearingBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                    if (aggrRoot.isExceededProcessingLimit()) {
                        FundPoolSharingTransactionAggrRoot transactionAggrRoot = context.getFundPoolSharingTransactionAggrRoot();
                        updateTransactionStatus(transactionAggrRoot, FundPoolSharingTransactionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void onFailure(TransactionSharingSettlementEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                    if (aggrRoot.isExceededProcessingLimit()) {
                        FundPoolSharingTransactionAggrRoot transactionAggrRoot = context.getFundPoolSharingTransactionAggrRoot();
                        updateTransactionStatus(transactionAggrRoot, FundPoolSharingTransactionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void doFinally(TransactionSharingSettlementEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

    protected void processPush(TransactionSharingSettlementEventContext context, FundPoolSharingTransBookAggrRoot aggrRoot
            , FundPoolSharingTransactionAggrRoot transaction) {
        FundClearingSettlementFlowCreateRequest.AccountModel payAccountModel = FundClearingSettlementFlowCreateRequest.AccountModel.builder()
                .type(aggrRoot.getPayeeType().getCode())
                .brandSn(aggrRoot.getPayeeInfo().getBrandSn())
                .merchantSn(aggrRoot.getPayeeInfo().getFundMerchantSn())
                .storeSn(aggrRoot.getPayeeInfo().getStoreSn())
                .build();
        FundClearingSettlementFlowCreateRequest.AccountModel receiveAccountModel = FundClearingSettlementFlowCreateRequest.AccountModel.builder()
                .type(aggrRoot.getReceiverType().getCode())
                .brandSn(aggrRoot.getReceiverInfo().getBrandSn())
                .merchantSn(aggrRoot.getReceiverInfo().getFundMerchantSn())
                .storeSn(aggrRoot.getReceiverInfo().getStoreSn())
                .build();
        FundClearingSettlementFlowCreateRequest.AmountModel amountModel;
        FundClearingSettlementFlowCreateRequest.BizInfoModel bizInfoModel;
        FundPoolAggrRoot fundPoolAggrRoot = context.getFundPoolAggrRoot();

        if (transaction.isBillTransaction()) {
            FundBillAggrRoot fundBillAggrRoot = fundBillDomainRepository.query(FundBillAggrQuery.builder()
                    .id(transaction.getFundBillId())
                    .status(FundBillStatusEnum.SHARING.getCode())
                    .build());
            amountModel = FundClearingSettlementFlowCreateRequest.AmountModel.builder()
                    .originAmount(fundBillAggrRoot.getAmount().getOriginAmount())
                    .fee(fundBillAggrRoot.getAmount().getFee())
                    .settleAmount(aggrRoot.getSharingAmount())
                    .build();
            bizInfoModel = FundClearingSettlementFlowCreateRequest.BizInfoModel.builder().build();
        } else {
            amountModel = FundClearingSettlementFlowCreateRequest.AmountModel.builder()
                    .originAmount(fundPoolAggrRoot.getAmount().getOriginAmount())
                    .fee(fundPoolAggrRoot.getAmount().getFee())
                    .settleAmount(aggrRoot.getSharingAmount())
                    .build();
            bizInfoModel = FundClearingSettlementFlowCreateRequest.BizInfoModel.builder().build();
        }

        FundBatchAggrRoot fundBatchAggrRoot =
                fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
        fundBatchAggrRoot.checkExist();

        FundClearingSettlementFlowCreateRequest request = FundClearingSettlementFlowCreateRequest.builder()
                .type(SETTLE_FLOW_TYPE_SHARING)
                .batchId(fundBatchAggrRoot.getSettleBatchId())
                .poolId(transaction.getFundPoolId())
                .transSn(transaction.getIdStr())
                .orderSn(transaction.getIdStr())
                .fromInfo(transaction.isPayTransaction() ? payAccountModel : receiveAccountModel)
                .toInfo(transaction.isPayTransaction() ? receiveAccountModel : payAccountModel)
                .amount(amountModel)
                .tradeInfo(FundClearingSettlementFlowCreateRequest.TradeInfoModel.builder()
                        .tradeTime(fundPoolAggrRoot.getDefaultFormatCtime())
                        .flowChannel(fundPoolAggrRoot.getBillSource().getCode())
                        .acquiringCompany(fundPoolAggrRoot.getAcquiringCompany())
                        .payWay(fundPoolAggrRoot.getPayway().getCode())
                        .settleType(Objects.equals(transaction.getType(), FundTransactionTypeEnum.PAYMENT) ?
                                SettleTypeEnum.SHARING.getCode() : SettleTypeEnum.SHARING_RETURN.getCode())
                        .build())
                .bizInfo(bizInfoModel)
                .build();

        for (int i = 0; i < DEFAULT_THRESHOLD; i++) {
            FundClearingSettlementFlowCreateResult result = settleClient.createSettlementFlow(request);
            if (result.isSuccess()) {
                updateBookStatus(aggrRoot, FundPoolSharingTransBookStatusEnum.PROCESSED);
                return;
            }
        }
        updateBookStatus(aggrRoot, FundPoolSharingTransBookStatusEnum.FAILED);
        sendMsgToFeishu(context);
    }

    protected void updateBookStatus(FundPoolSharingTransBookAggrRoot aggrRoot, FundPoolSharingTransBookStatusEnum statusEnum) {
        aggrRoot.updateStatus(statusEnum);
        fundPoolSharingTransBookDomainRepository.save(aggrRoot);
    }

    protected void updateTransactionStatus(FundPoolSharingTransactionAggrRoot aggrRoot, FundPoolSharingTransactionStatusEnum statusEnum) {
        aggrRoot.updateStatus(statusEnum);
        fundPoolSharingTransactionDomainRepository.save(aggrRoot);
    }

    protected void sendMsgToFeishu(TransactionSharingSettlementEventContext context) {
        try {
            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
            FeishuSendRequest request = FeishuSendRequest
                    .genEventProcessAlarmRequest(eventAggrRoot.getType().getDesc()
                            , eventAggrRoot.getId()
                            , DEFAULT_THRESHOLD
                            , DEFAULT_THRESHOLD);
            feishuClient.sendNotice(request);
        } catch (Throwable ignored) {
        }
    }


}
