package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.ClearingNotifyEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class ClearingNotifyStrategy extends AbstractBehaviorTreeStrategy<ClearingNotifyEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.CLEARING_NOTIFY;

    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<ClearingNotifyEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            ClearingNotifyEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final ClearingNotifyEventContext context;

        public FundBillEntryRunnable(ClearingNotifyEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(ClearingNotifyEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(ClearingNotifyEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // 查询批次
                            FundBatchAggrRoot fundBatchAggrRoot =
                                    fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
                            fundBatchAggrRoot.checkExist();
                            context.bindFundBatchAggrRoot(fundBatchAggrRoot);

                            if (context.isClearingSuccess()) {
                                fundBatchAggrRoot.updateStatus(FundBatchStatusEnum.SUCCESS);  // todo 事务处理
                                fundBatchDomainRepository.save(fundBatchAggrRoot);
                                eventDomainRepository.save(context.genFundBatchBillEntryToPoolEventAggrRoot());
                                eventDomainRepository.save(context.genFundBatchBillEntryToPoolMonitorEventAggrRoot());

                            }
                            eventAggrRoot.processSuccess("清分成功");
                        }

                        @Override
                        protected void postInvokeExternal(
                                ClearingNotifyEventContext ClearingNotifyEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                ClearingNotifyEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(
                                ClearingNotifyEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(ClearingNotifyEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
