package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.google.common.collect.Lists;
import com.shouqianba.trade.fund.clearing.api.request.model.FundBillSortModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundPoolCursorModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBatchFullClearingEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.EventDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementBatchCreateResult;
import com.shouqianba.trade.fund.clearing.infrastructure.support.service.ClearingAccountSupportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBatchFullClearingStrategy extends AbstractBehaviorTreeStrategy<FundBatchFullClearingEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FULL_CLEARING;
    private static final int DEFAULT_PAGE_SIZE = 100;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private SettleClient settleClient;
    @Resource
    private ClearingAccountSupportService clearingAccountSupportService;
    @Resource
    private EventDomainRepository eventDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBatchFullClearingEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundBatchFullClearingEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBatchFullClearingEventContext context;

        public FundBillEntryRunnable(FundBatchFullClearingEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBatchFullClearingEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBatchFullClearingEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // 查询批次
                            FundBatchAggrRoot fundBatchAggrRoot =
                                    fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
                            fundBatchAggrRoot.checkExist();
                            context.bindFundBatchAggrRoot(fundBatchAggrRoot);

                            // 查询账户
                            ClearingAccountSupportService.AccountInfoResult accountInfoResult =
                                    clearingAccountSupportService.processAccountInfo(
                                            context.genFundClearingBillAccountModel());
                            accountInfoResult.checkExist();
                            context.bindClearingAccountInfoResult(accountInfoResult);

                            // 调用结算服务
                            FundClearingSettlementBatchCreateResult fundClearingSettlementBatchCreateResult =
                                    settleClient.createSettlementBatch(
                                            context.genFundClearingSettlementBatchCreateRequest());
                            context.bindFundClearingSettlementBatchCreateResult(
                                    fundClearingSettlementBatchCreateResult);
                            if (fundClearingSettlementBatchCreateResult.isFailed()) {
                                eventAggrRoot.processFailure("[入账结算批次明细推送]创建入账批次失败");
                                return;
                            }

                            int pageSize = DEFAULT_PAGE_SIZE;
                            int processedCount = 0;
                            boolean hasMore = true;
                            String endCursor = null;
                            while (hasMore) {
                                List<FundBillAggrRoot> fundBills = fundBillDomainRepository.batchQuery(FundBillAggrQuery
                                        .builder()
                                        .fundBatchId(fundBatchAggrRoot.getId())
                                        .querySize(pageSize)
                                        .cursorField(FundPoolCursorModel.FundBillCursorFieldEnum.ID.getInnerField())
                                        .endCursor(endCursor)
                                        .sortField(FundBillSortModel.FundBillSortFieldEnum.ID.getInnerField())
                                        .isDesc(Boolean.FALSE)
                                        .build());

                                if (CollectionUtils.isEmpty(fundBills)) {
                                    hasMore = false;
                                    continue;
                                }
                                List<EventAggrRoot> events = Lists.newArrayList();
                                for (FundBillAggrRoot fundBill : fundBills) {
                                    events.add(context.genCreateSettlementFlowEvent(fundBill));
                                }

                                try {
                                    eventDomainRepository.batchSave(events);
                                } catch (DuplicateKeyException ignore) {

                                }

                                if (fundBills.size() < pageSize) {
                                    hasMore = false;
                                } else {
                                    endCursor = fundBills
                                            .getLast()
                                            .getIdStr();
                                }
                            }

                            try {
                                eventDomainRepository.save(context.genCreateSettlementFlowMonitorEvent());
                            } catch (
                                    DuplicateKeyException ignore) {
                            }

                            fundBatchAggrRoot.updateSettleBatchId(fundClearingSettlementBatchCreateResult.getBatchId());
                            fundBatchDomainRepository.save(fundBatchAggrRoot);
                            log.info("[入账结算批次明细推送]>>>>>>处理完成，总处理资金池数量: {}", processedCount);

                        }

                        @Override
                        protected void postInvokeExternal(
                                FundBatchFullClearingEventContext FundBatchFullClearingEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                FundBatchFullClearingEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundBatchFullClearingEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBatchFullClearingEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
