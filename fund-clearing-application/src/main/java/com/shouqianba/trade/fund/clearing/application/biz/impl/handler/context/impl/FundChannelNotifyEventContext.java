package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.AbstractBehaviorTreeContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardValueDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.ConfigFundCollectRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.query.ConfigFundCollectRuleAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrulebelonging.model.query.ConfigFundCollectRuleBelongAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.*;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req.BrandDetailInfoGetRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Getter
@Component
public class FundChannelNotifyEventContext extends AbstractBehaviorTreeContext<EventContentFundChannelNotifyVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_CHANNEL_NOTIFY;

    private EventContentFundChannelNotifyVO eventContent;
    private BrandDetailInfoGetResult brandDetailInfoGetResult;
    private FundBatchAggrRoot fundBatchAggrRoot;
    private FundPoolAggrRoot fundPoolAggrRoot;
    private FundBillAggrRoot fundBillAggrRoot;
    private ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot;

    static {
        registerContext(EVENT_TYPE, new FundChannelNotifyEventContext());
    }

    private FundChannelNotifyEventContext() {
        super();
    }

    private FundChannelNotifyEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentFundChannelNotifyVO> rebuildContext(EventHandlerContext context) {
        return new FundChannelNotifyEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentFundChannelNotifyVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentFundChannelNotifyVO.class);
        }
        return eventContent;
    }

    public FundBillAggrQuery genFundBillAggrQuery() {
        return FundBillAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public ConfigFundCollectRuleBelongAggrQuery genConfigFundCollectRuleBelongQuery() {
        return ConfigFundCollectRuleBelongAggrQuery
                .builder()
                .id(fundBillAggrRoot.getConfigFundCollectRuleBelongId())
                .build();
    }

    public void bindFundBillAggrRoot(FundBillAggrRoot fundBillAggrRoot) {
        this.fundBillAggrRoot = fundBillAggrRoot;
    }

    public void bindFundPoolAggrRoot(FundPoolAggrRoot first) {
        this.fundPoolAggrRoot = first;
    }

    public ConfigBlackboardAggrRoot genConfigBlackboardAggrRoot() {
        return ConfigBlackboardAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genConfigBlackboardId())
                .valueDomain(ConfigBlackboardValueDomainVO
                        .builder()
                        .valueData(blackboard.toJsonNode())
                        .build())
                .optionalBuilder()
                .ext(ConfigBlackboardExtVO
                        .builder()
                        .remark("入账黑板")
                        .build())
                .build();
    }

    @Override
    public Long getBehaviorTreeId() {
        return fundBillAggrRoot.getBehaviorTreeId();
    }

    @Override
    public ClearingCustomBlackboard genClearingCustomBlackboard() {
        ClearingCustomBlackboard blackboard = new ClearingCustomBlackboard.ClearingCustomBlackboardBuilder().build();
        blackboard.setFundBillAggrRootId(fundBillAggrRoot.getIdStr());
        blackboard.setRuleBelongAggrRootId(fundBillAggrRoot.getConfigFundCollectRuleBelongIdStr());
        return blackboard;
    }

    public void bindConfigFundCollectRuleAggrRoot(ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot) {
        this.configFundCollectRuleAggrRoot = configFundCollectRuleAggrRoot;
    }

    public FundBatchAggrQuery genFundBatchAggrQuery() {
        EventContentFundChannelNotifyVO bizContent = getBizContent();
        return FundBatchAggrQuery
                .builder()
                .brandSn(brandDetailInfoGetResult.getBrandSn())
                .acquiringCompany(bizContent.getSettlementChannel())
                .settleTime(bizContent.genSettleTime()) // todo 等用户把账单来源从清分渠道去掉
                .id(fundPoolAggrRoot.getBatchId())
                .build();
    }

    public BrandDetailInfoGetRequest genBrandDetailInfoGetRequest() {
        return BrandDetailInfoGetRequest
                .builder()
                .brandId(getBizContent().getBrandId())
                .build();
    }

    public void bindBrandDetailInfoGetResult(BrandDetailInfoGetResult brandDetailInfo) {
        this.brandDetailInfoGetResult = brandDetailInfo;
    }

    public void bindFundBatchAggrRoot(FundBatchAggrRoot fundBatchAggrRoot) {
        this.fundBatchAggrRoot = fundBatchAggrRoot;
    }

    public boolean isFullClearingNotify() {
        Long needClearingAmount = fundBatchAggrRoot
                .getAmount()
                .getNeedClearingAmount();
        return getBizContent().getAmount() >= needClearingAmount;
    }

    public boolean isPartialClearingNotify() {
        return !isFullClearingNotify();
    }

    public EventAggrRoot genFullClearingEvent() {
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.FULL_CLEARING)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentFullClearingVO
                                .builder()
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getBrandSn())
                        .build())
                .build();
    }

    public EventAggrRoot genPartialClearingEvent() {
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.PARTIAL_CLEARING)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(JsonUtils
                                .convertToObject(getBizContent(), EventContentPartialClearingVO.class)
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getBrandSn())
                        .build())
                .build();
    }
}
