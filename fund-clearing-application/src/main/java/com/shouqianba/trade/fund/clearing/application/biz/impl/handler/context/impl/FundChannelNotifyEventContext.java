package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.*;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req.BrandDetailInfoGetRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Getter
@Component
public class FundChannelNotifyEventContext extends EventStrategyContext<EventContentFundChannelNotifyVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_CHANNEL_NOTIFY;

    private EventContentFundChannelNotifyVO eventContent;
    private BrandDetailInfoGetResult brandDetailInfoGetResult;
    private FundBatchAggrRoot fundBatchAggrRoot;

    static {
        registerContext(EVENT_TYPE, new FundChannelNotifyEventContext());
    }

    private FundChannelNotifyEventContext() {
        super();
    }

    private FundChannelNotifyEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentFundChannelNotifyVO> rebuildContext(EventHandlerContext context) {
        return new FundChannelNotifyEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentFundChannelNotifyVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentFundChannelNotifyVO.class);
        }
        return eventContent;
    }

    public FundBillAggrQuery genFundBillAggrQuery() {
        return FundBillAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public BrandDetailInfoGetRequest genBrandDetailInfoGetRequest() {
        return BrandDetailInfoGetRequest
                .builder()
                .brandId(getBizContent().getBrandId())
                .build();
    }

    public void bindBrandDetailInfoGetResult(BrandDetailInfoGetResult brandDetailInfo) {
        this.brandDetailInfoGetResult = brandDetailInfo;
    }

    public void bindFundBatchAggrRoot(FundBatchAggrRoot fundBatchAggrRoot) {
        this.fundBatchAggrRoot = fundBatchAggrRoot;
    }

    public boolean isFullClearingNotify() {
        Long needClearingAmount = fundBatchAggrRoot
                .getAmount()
                .getNeedClearingAmount();
        return getBizContent().getAmount() >= needClearingAmount;
    }

    public boolean isPartialClearingNotify() {
        return !isFullClearingNotify();
    }

    public EventAggrRoot genFullClearingEvent() {
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.FULL_CLEARING)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentFullClearingVO
                                .builder()
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getBrandSn())
                        .build())
                .build();
    }

    public EventAggrRoot genPartialClearingEvent() {
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.PARTIAL_CLEARING)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(JsonUtils
                                .convertToObject(getBizContent(), EventContentPartialClearingVO.class)
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getBrandSn())
                        .build())
                .build();
    }

    public FundBatchAggrQuery genFundBatchAggrQuery() {
        return FundBatchAggrQuery
                .builder()
                .payeeType((byte)2) // todo 抽取常量
                .brandSn(brandDetailInfoGetResult.getBrandSn())
                .settleTime(getBizContent().genSettleTime())
                .acquiringCompany(getBizContent().getSettlementChannel())
                .build();
    }
}
