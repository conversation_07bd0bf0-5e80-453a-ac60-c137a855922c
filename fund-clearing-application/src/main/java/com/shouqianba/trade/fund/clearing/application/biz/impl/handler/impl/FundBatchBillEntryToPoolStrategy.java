package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.google.common.collect.Lists;
import com.shouqianba.trade.fund.clearing.api.request.model.FundBillSortModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundPoolCursorModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBatchBillEntryToPoolEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.ConfigFundCollectRuleDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.BrandBusinessClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBatchBillEntryToPoolStrategy extends AbstractBehaviorTreeStrategy<FundBatchBillEntryToPoolEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_BATCH_BILL_ENTRY_TO_POOL;
    private static final int DEFAULT_PAGE_SIZE = 100;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private ConfigFundCollectRuleDomainRepository ruleDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private SettleClient settleClient;
    @Resource
    private BrandBusinessClient brandBusinessClient;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBatchBillEntryToPoolEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundBatchBillEntryToPoolEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBatchBillEntryToPoolEventContext context;

        public FundBillEntryRunnable(FundBatchBillEntryToPoolEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBatchBillEntryToPoolEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBatchBillEntryToPoolEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // 查询批次
                            FundBatchAggrRoot fundBatchAggrRoot =
                                    fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
                            fundBatchAggrRoot.checkExist();
                            context.bindFundBatchAggrRoot(fundBatchAggrRoot);


                            int pageSize = DEFAULT_PAGE_SIZE;
                            boolean hasMore = true;
                            String endCursor = null;
                            while (hasMore) {
                                List<FundBillAggrRoot> fundBills = fundBillDomainRepository.batchQuery(FundBillAggrQuery
                                        .builder()
                                        .fundBatchId(fundBatchAggrRoot.getId())
                                        .querySize(pageSize)
                                        .cursorField(FundPoolCursorModel.FundBillCursorFieldEnum.ID.getInnerField())
                                        .endCursor(endCursor)
                                        .sortField(FundBillSortModel.FundBillSortFieldEnum.ID.getInnerField())
                                        .isDesc(Boolean.FALSE)
                                        .build());

                                if (CollectionUtils.isEmpty(fundBills)) {
                                    hasMore = false;
                                    continue;
                                }
                                List<EventAggrRoot> events = Lists.newArrayList();
                                for (FundBillAggrRoot fundBill : fundBills) {
                                    events.add(context.genFundBIllEntryToPoolEvent(fundBill));
                                }

                                if (fundBills.size() < pageSize) {
                                    hasMore = false;
                                } else {
                                    endCursor = fundBills
                                            .getLast()
                                            .getIdStr();
                                }
                            }

                        }

                        @Override
                        protected void postInvokeExternal(
                                FundBatchBillEntryToPoolEventContext FundBatchBillEntryToPoolEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                FundBatchBillEntryToPoolEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(
                                FundBatchBillEntryToPoolEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBatchBillEntryToPoolEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
