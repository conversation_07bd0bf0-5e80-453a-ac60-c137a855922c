package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBillCreateSettlementFlowMonitorEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.ConfigFundCollectRuleDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.BrandBusinessClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementFlowFinishCreateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBillCreateSettlementFlowMonitorStrategy extends AbstractBehaviorTreeStrategy<FundBillCreateSettlementFlowMonitorEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.CREATE_SETTLEMENT_FLOW_MONITOR;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private ConfigFundCollectRuleDomainRepository ruleDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private SettleClient settleClient;
    @Resource
    private BrandBusinessClient brandBusinessClient;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBillCreateSettlementFlowMonitorEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundBillCreateSettlementFlowMonitorEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBillCreateSettlementFlowMonitorEventContext context;

        public FundBillEntryRunnable(FundBillCreateSettlementFlowMonitorEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBillCreateSettlementFlowMonitorEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBillCreateSettlementFlowMonitorEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // 查询批次
                            FundBatchAggrRoot fundBatchAggrRoot =
                                    fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
                            fundBatchAggrRoot.checkExist();
                            context.bindFundBatchAggrRoot(fundBatchAggrRoot);

                            // 1. 查询资金账单
                            Long count =
                                    fundBillDomainRepository.count(context.genFundBillAggrQuery());
                            if (count > 0) {
                                log.error("[事件处理]>>>>>>事件处理失败, 事件ID: {}, 错误信息: 资金单已存在",
                                        eventAggrRoot.getId());
                                return;
                            }
                            FundClearingSettlementFlowFinishCreateResult fundClearingSettlementFlowFinishCreateResult =
                                    settleClient.finishCreateSettlementFlow(
                                            context.genFundClearingSettlementFlowFinishCreateRequest());
                            if (fundClearingSettlementFlowFinishCreateResult.isFailed()) {
                                eventAggrRoot.processFailure("资金单创建失败" );
                                return;
                            }
                            fundBatchAggrRoot.updateStatus(FundBatchStatusEnum.SETTLING);
                            eventAggrRoot.processSuccess("资金单已全部完成入账结算");
                        }

                        @Override
                        protected void postInvokeExternal(
                                FundBillCreateSettlementFlowMonitorEventContext FundBillCreateSettlementFlowMonitorEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                FundBillCreateSettlementFlowMonitorEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(
                                FundBillCreateSettlementFlowMonitorEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBillCreateSettlementFlowMonitorEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
