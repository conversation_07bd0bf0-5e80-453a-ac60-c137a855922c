package com.shouqianba.trade.fund.clearing.application.adapter.mq;

import com.shouqianba.trade.fund.clearing.application.aop.annotation.FundClearingEntry;
import com.shouqianba.trade.fund.clearing.application.biz.context.impl.event.EventProcessContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.BrandEnrollBiz;
import com.shouqianba.trade.fund.clearing.application.biz.impl.event.EventProcessBiz;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.common.template.WithoutResultTemplate;
import com.shouqianba.trade.fund.clearing.common.util.KafkaAvroUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.EventDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.query.EventAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.config.support.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/1/2 Time: 9:09 PM
 */
@Slf4j
@Component
@ConditionalOnExpression("${kafka.consumer.enable}")
public class KafkaConsumer {
    private static final ExecutorService DEFAULT_EXECUTOR_SERVICE = Executors.newVirtualThreadPerTaskExecutor();
    private Semaphore semaphore;

    @Resource
    private KafkaAvroUtils kafkaAvroUtils;
    @Resource
    private BrandEnrollBiz brandEnrollBiz;
    @Resource
    private EventProcessBiz eventProcessBiz;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Value("${spring.kafka.consumer.max-poll-records}")
    private int permits;

    @PostConstruct
    private void init() {
        semaphore = new Semaphore(permits, true);
    }

    @PreDestroy
    private void destroy() {
        DEFAULT_EXECUTOR_SERVICE.shutdown();
        boolean res = false;
        try {
            res = DEFAULT_EXECUTOR_SERVICE.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("[清分系统事件消费]>>>>>>线程池关闭过程被中断, 异常栈: ", e);
        }
        if (!res) {
            log.warn("[清分系统事件消费]>>>>>>线程池未在10秒内关闭成功");
        }
    }

    //@FundClearingEntry(skipValidate = true, skipLoggingParams = true)
    //@KafkaListener(id = "upay-clearing-brand-business-consumer",
    //        groupId = "${spring.kafka.consumer.channel.brand.group-id}",
    //        topics = "${spring.kafka.consumer.channel.brand.topic.brand-business}",
    //        containerFactory = "channelBrandKafkaContainerFactory")
    //public void consumeBrandEnroll(ConsumerRecord<String, GenericRecord> record) {
    //    try {
    //        GenericRecord genericRecord = record.value();
    //        if (Objects.isNull(genericRecord)) {
    //            log.warn("[品牌报备消息消费]解析消息为空");
    //            return;
    //        }
    //        BrandEventModel event = kafkaAvroUtils.convertObject(genericRecord, new TypeReference<>() {
    //        });
    //        // 过滤非报备消息
    //        if (event.isNotBrandEnrollMessage()) {
    //            return;
    //        }
    //        BrandEnrollEventModel enrollEvent = JsonUtils
    //                .parseObject(event.getMessage(), BrandEnrollEventModel.class);
    //        if (Objects.isNull(enrollEvent)) {
    //            log.warn("[品牌报备消息消费]解析失败,消息内容: {}", genericRecord);
    //            return;
    //        }
    //        BrandEnrollContext context = BrandEnrollContext.newInstance(enrollEvent);
    //        brandEnrollBiz.process(context);
    //    } catch (Exception e) {
    //        log.error("[品牌报备消息消费] 异常栈:", e);
    //    }
    //}

    @KafkaListener(id = "fund-clearing-event-consumer"
            , groupId = "${spring.kafka.consumer.group-id}"
            , topics = {"${spring.kafka.consumer.topic.fund-clearing}"})
    @FundClearingEntry(skipValidate = true, skipLoggingParams = true)
    public void consumeAcquiringEvent(List<ConsumerRecord<?, ?>> records) {
        log.info("[清分系统事件消息]>>>>>>批量拉取长度: {}", records.size());
        for (ConsumerRecord<?, ?> consumerRecord : records) {
            log.debug("[清分系统事件消息]>>>>>>topic: {}, partition: {}, offset: {}" +
                            ", timestamp: {}, key: {}, value: {}"
                    , consumerRecord.topic(), consumerRecord.partition()
                    , consumerRecord.offset(), consumerRecord.timestamp()
                    , consumerRecord.key(), consumerRecord.value());
            Object valueObj = consumerRecord.value();
            if (Objects.isNull(valueObj)) {
                log.warn("[清分系统事件消息]>>>>>>消息Value为空");
                continue;
            }
            Long eventId = Long.parseLong((String) valueObj);
            EventAggrRoot aggrRoot = eventDomainRepository
                    .query(EventAggrQuery.builder()
                            .id(eventId)
                            .build());
            if (aggrRoot.isNotExist()) {
                log.debug("[清分系统事件消息]>>>>>>事件不存在, 事件ID: {}", aggrRoot.getId());
                continue;
            }
            if (aggrRoot.isProcessed()) {
                log.debug("[清分系统事件消息]>>>>>>事件已处理(跳过), 事件ID: {}", aggrRoot.getId());
                continue;
            }
            processEvent(aggrRoot);
        }
    }

    private void processEvent(EventAggrRoot aggrRoot) {
        DEFAULT_EXECUTOR_SERVICE.execute(() -> {
            boolean acquired = false;
            try {
                acquired = semaphore.tryAcquire();
                if (!acquired) {
                    log.warn("[清分系统事件消费]>>>>>>已达到最大并发限制, 消息处理被跳过");
                    return;
                }

                InvokeProcessor.processWithoutResult(aggrRoot, new WithoutResultTemplate<>() {
                    @Override
                    protected void invoke(EventAggrRoot aggrRoot) throws Throwable {
                        try {
                            //标记为次要数据源
                            DataSourceContextHolder.markAsPeripheryDataSource();

                            //事件处理
                            EventProcessContext context = EventProcessContext.newInstance(aggrRoot);
                            eventProcessBiz.process(context);
                        } finally {
                            //清除数据源标记
                            DataSourceContextHolder.clearDataSourceKey();
                        }
                    }

                    @Override
                    protected void onBizFailure(EventAggrRoot aggrRoot, FundClearingBizException e) {
                        log.warn("[清分系统事件消费]>>>>>>事件消费失败, 业务异常, 异常栈: ", e);
                    }

                    @Override
                    protected void onFailure(EventAggrRoot aggrRoot, Throwable throwable) {
                        log.error("[清分系统事件消费]>>>>>>事件消费失败, 未知异常, 异常栈: ", throwable);
                    }
                });
            } finally {
                if (acquired) {
                    semaphore.release();
                }
            }
        });
    }

}