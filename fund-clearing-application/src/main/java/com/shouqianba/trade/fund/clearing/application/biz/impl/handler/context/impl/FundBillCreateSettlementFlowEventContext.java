package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.AbstractBehaviorTreeContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardValueDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.ConfigFundCollectRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.query.ConfigFundCollectRuleAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrulebelonging.model.query.ConfigFundCollectRuleBelongAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentCreateSettlementFlowVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req.BrandDetailInfoGetRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementFlowCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Getter
@Component
public class FundBillCreateSettlementFlowEventContext extends AbstractBehaviorTreeContext<EventContentCreateSettlementFlowVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.CREATE_SETTLEMENT_FLOW;
    public static final byte FLOW_TYPE = (byte) 1;
    public static final byte FROM_TYPE = (byte) 1;


    private EventContentCreateSettlementFlowVO eventContent;
    private FundPoolAggrRoot fundPoolAggrRoot;
    private FundBillAggrRoot fundBillAggrRoot;
    private BrandDetailInfoGetResult brandDetailInfoGetResult;
    private ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot;

    static {
        registerContext(EVENT_TYPE, new FundBillCreateSettlementFlowEventContext());
    }

    private FundBillCreateSettlementFlowEventContext() {
        super();
    }

    private FundBillCreateSettlementFlowEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentCreateSettlementFlowVO> rebuildContext(EventHandlerContext context) {
        return new FundBillCreateSettlementFlowEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentCreateSettlementFlowVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentCreateSettlementFlowVO.class);
        }
        return eventContent;
    }

    public FundBillAggrQuery genFundBillAggrQuery() {
        return FundBillAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public ConfigFundCollectRuleBelongAggrQuery genConfigFundCollectRuleBelongQuery() {
        return ConfigFundCollectRuleBelongAggrQuery
                .builder()
                .id(fundBillAggrRoot.getConfigFundCollectRuleBelongId())
                .build();
    }

    public void bindFundBillAggrRoot(FundBillAggrRoot fundBillAggrRoot) {
        this.fundBillAggrRoot = fundBillAggrRoot;
    }

    public void bindFundPoolAggrRoot(FundPoolAggrRoot first) {
        this.fundPoolAggrRoot = first;
    }

    public ConfigBlackboardAggrRoot genConfigBlackboardAggrRoot() {
        return ConfigBlackboardAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genConfigBlackboardId())
                .valueDomain(ConfigBlackboardValueDomainVO
                        .builder()
                        .valueData(blackboard.toJsonNode())
                        .build())
                .optionalBuilder()
                .ext(ConfigBlackboardExtVO
                        .builder()
                        .remark("入账黑板")
                        .build())
                .build();
    }

    @Override
    public Long getBehaviorTreeId() {
        return fundBillAggrRoot.getBehaviorTreeId();
    }

    @Override
    public ClearingCustomBlackboard genClearingCustomBlackboard() {
        ClearingCustomBlackboard blackboard = new ClearingCustomBlackboard.ClearingCustomBlackboardBuilder().build();
        blackboard.setFundBillAggrRootId(fundBillAggrRoot.getIdStr());
        blackboard.setRuleBelongAggrRootId(fundBillAggrRoot.getConfigFundCollectRuleBelongIdStr());
        return blackboard;
    }

    public ConfigFundCollectRuleAggrQuery genConfigFundCollectRuleQuery() {
        return ConfigFundCollectRuleAggrQuery
                .builder()
                .id(getFundBillAggrRoot().getCollectionRuleId())
                .build();
    }

    public void bindConfigFundCollectRuleAggrRoot(ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot) {
        this.configFundCollectRuleAggrRoot = configFundCollectRuleAggrRoot;
    }


    public FundClearingSettlementFlowCreateRequest genFundClearingSettlementFlowCreateRequest() {
        return FundClearingSettlementFlowCreateRequest
                .builder()
                .type(FLOW_TYPE)                // 流水类型:1-入账结算 2-分账结算
                .transSn(fundBillAggrRoot.getTransSn())        // 交易单号
                .orderSn(fundBillAggrRoot.getOrderSn())        // 业务订单号
                .batchId(getBizContent().getSettleBatchId())              // 批次号
                .poolId(fundBillAggrRoot.getFundPoolId())
                .fromInfo(FundClearingSettlementFlowCreateRequest.AccountModel
                        .builder()
                        .type(FROM_TYPE)        // 账户类型：1-品牌 2-商户 3-门店
                        .brandSn(fundBillAggrRoot.getBrandSn())   // 品牌编号
                        .merchantSn(brandDetailInfoGetResult.getMerchantSn()) // 商户编号
                        .build())
                .toInfo(FundClearingSettlementFlowCreateRequest.AccountModel
                        .builder()
                        .type(fundBillAggrRoot
                                .getPayeeType()
                                .getCode())        // 账户类型：1-品牌 2-商户 3-门店
                        .brandSn(fundBillAggrRoot.getBrandSn())   // 品牌编号
                        .merchantSn(fundBillAggrRoot.getMerchantSn()) // 商户编号
                        .storeSn(fundBillAggrRoot.getStoreSn())   // 门店编号
                        .channelMerchantSn(fundBillAggrRoot.getDefaultChannelMerchantSn()) // 渠道商户编号
                        .build())
                .amount(FundClearingSettlementFlowCreateRequest.AmountModel
                        .builder()
                        .originAmount(fundBillAggrRoot.getOriginAmount())   // 原始金额
                        .fee(fundBillAggrRoot.getFee())              // 手续费
                        .settleAmount(fundBillAggrRoot.getSettlementAmount())    // 结算金额
                        .build())
                .tradeInfo(FundClearingSettlementFlowCreateRequest.TradeInfoModel
                        .builder()
                        .settleType(fundBillAggrRoot.getSettleTypeCode())   // 结算类型：1-结算 2-收款 3-退款 4-分账 5-分账回退
                        .flowChannel(fundBillAggrRoot
                                .getBillSource()
                                .getCode())  // 流水渠道：1-收钱吧 2-标准收单接口 3-美团 4-饿了么 5-品牌上送 6-抖音
                        .acquiringCompany(fundBillAggrRoot.getAcquiringCompany()) // 收单机构
                        .payWay(fundBillAggrRoot.getPayWayCode())       // 支付方式
                        .tradeTime(fundBillAggrRoot.getTradeTimeStr()) // 交易时间
                        .channelTransSn(fundBillAggrRoot.getChannelTransSn()) // 渠道交易流水号
                        .channelOrderSn(fundBillAggrRoot.getChannelOrderSn()) // 渠道订单号
                        .build())
                .bizInfo(FundClearingSettlementFlowCreateRequest.BizInfoModel.builder().build())
                .build();
    }

    public FundBatchAggrQuery genFundBatchAggrQuery() {
        return FundBatchAggrQuery
                .builder()
                .id(fundBillAggrRoot.getFundBatchId())
                .build();
    }



    public BrandDetailInfoGetRequest genBrandDetailInfoGetRequest() {
        return BrandDetailInfoGetRequest
                .builder()
                .brandId(fundBillAggrRoot.getPayeeInfo().getBrandId())
                .build();
    }

    public void bindBrandDetailInfoGetResult(BrandDetailInfoGetResult brandDetailInfoGetResult) {
        this.brandDetailInfoGetResult = brandDetailInfoGetResult;
    }
}
