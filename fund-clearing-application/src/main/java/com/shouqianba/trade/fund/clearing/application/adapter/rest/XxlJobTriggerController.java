package com.shouqianba.trade.fund.clearing.application.adapter.rest;

import com.shouqianba.trade.fund.clearing.application.adapter.rest.request.EntryBatchSettlementTriggerRequest;
import com.shouqianba.trade.fund.clearing.application.adapter.rest.request.EntryPoolSettlementTriggerRequest;
import com.shouqianba.trade.fund.clearing.application.adapter.rest.request.SharingSettlementTriggerRequest;
import com.shouqianba.trade.fund.clearing.application.biz.context.EntryBatchSettlementTriggerContext;
import com.shouqianba.trade.fund.clearing.application.biz.context.SharingSettlementTriggerContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.EntryBatchSettlementTriggerBiz;
import com.shouqianba.trade.fund.clearing.application.biz.impl.SharingSettlementTriggerBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/5/25 Time: 9:41 AM
 */
@Slf4j
@RestController
@RequestMapping("/fund-bill/xxl-job")
public class XxlJobTriggerController {

    private static final ResponseEntity<String> SUCCESS_RESULT = ResponseEntity.ok("success");

    @Resource
    private EntryPoolSettlementTriggerBiz entryPoolSettlementTriggerBiz;
    @Resource
    private EntryBatchSettlementTriggerBiz entryBatchSettlementTriggerBiz;
    @Resource
    private SharingSettlementTriggerBiz sharingSettlementTriggerBiz;

    @PostMapping("/poolEntrySettlement/trigger")
    public ResponseEntity<String> triggerPoolSettlement(@RequestBody EntryPoolSettlementTriggerRequest request) {
        log.info("[入账结算触发]>>>>>>开始触发账单入账结算, 请求参数: {}", request);
        EntryPoolSettlementTriggerContext context = EntryPoolSettlementTriggerContext.newInstance(request);
        entryPoolSettlementTriggerBiz.process(context);
        return SUCCESS_RESULT;
    }

    @PostMapping("/batchEntrySettlement/trigger")
    public ResponseEntity<String> triggerBatchSettlement(@RequestBody EntryBatchSettlementTriggerRequest request) {
        log.info("[入账结算触发]>>>>>>开始触发账单入账结算, 请求参数: {}", request);
        EntryBatchSettlementTriggerContext context = EntryBatchSettlementTriggerContext.newInstance(request);
        entryBatchSettlementTriggerBiz.process(context);
        return SUCCESS_RESULT;
    }

    //todo 做成动作写在分账行为树后
    @PostMapping("/sharing-settlement/trigger")
    public ResponseEntity<String> triggerSharingSettlement(@RequestBody SharingSettlementTriggerRequest request) {
        log.info("[分账结算触发]>>>>>>开始触发账单分账结算, 请求参数: {}", request);
        SharingSettlementTriggerContext context = SharingSettlementTriggerContext.newInstance(request);
        sharingSettlementTriggerBiz.process(context);
        return SUCCESS_RESULT;
    }
}
