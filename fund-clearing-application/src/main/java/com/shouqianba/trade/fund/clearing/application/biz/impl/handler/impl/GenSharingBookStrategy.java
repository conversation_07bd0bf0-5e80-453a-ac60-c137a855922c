package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillAccountModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundTransactionCursorModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundTransactionSortModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.GenSharingBookEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.FundPoolDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.FundPoolStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.vo.FundPoolBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.FundPoolFlowDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.FundPoolFlowAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.FundPoolFlowAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowBusinessTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.enums.FundPoolFlowTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.vo.FundPoolFlowAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolflow.model.vo.FundPoolFlowBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.FundPoolSharingTransactionDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.FundPoolSharingTransactionAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.enums.FundPoolSharingTransactionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransaction.model.vo.ReceiverVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.FundPoolSharingTransBookDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.FundPoolSharingTransBookAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.FundPoolSharingTransBookAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.enums.FundPoolSharingTransBookStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpoolsharingtransbook.model.vo.FundPoolSharingTransBookAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.ReceiverDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.model.ReceiverAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.receiver.model.ReceiverAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.service.ClearingAccountSupportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GenSharingBookStrategy extends AbstractBehaviorTreeStrategy<GenSharingBookEventContext> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.GEN_SHARING_BOOK;
    private final Map<String, ReceiverAggrRoot> receiverCache = new HashMap<>();

    @Resource
    private FundPoolSharingTransBookDomainRepository fundPoolSharingTransBookDomainRepository;
    @Resource
    private FundPoolSharingTransactionDomainRepository transactionDomainRepository;
    @Resource
    private FundPoolDomainRepository fundPoolDomainRepository;
    @Resource
    private FundPoolFlowDomainRepository fundPoolFlowDomainRepository;
    @Resource
    private ReceiverDomainRepository receiverDomainRepository;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private ClearingAccountSupportService clearingAccountSupportService;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<GenSharingBookEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(GenSharingBookEventContext context) {
        return new GenBillPayTransactionRunnable(context);
    }

    public class GenBillPayTransactionRunnable extends IndependentEventStrategyRunnable {
        private final GenSharingBookEventContext context;

        public GenBillPayTransactionRunnable(GenSharingBookEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(GenSharingBookEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(GenSharingBookEventContext context) throws Throwable {
                    FundPoolAggrRoot fundPoolAggrRoot = fundPoolDomainRepository.query(context.genFundPoolAggrQuery());
                    fundPoolAggrRoot.checkExist();
                    context.bindFundPoolAggrRoot(fundPoolAggrRoot);

                    genAndSaveFundPoolSharingBook(context);
                }

                @Override
                protected void postInvokeExternal(GenSharingBookEventContext GenSharingBookEventContext) {
                    updateFundPoolActionStatus(context, SharingActionStatusEnum.SUCCESS);
                    context.bindBlackboardId(context.getBlackboardId());
                    context.bindBehaviorTreeId(context.getBehaviorTreeId());
                    executeBehaviorTreeAndProcessEventResult(context);
                }

                @Override
                protected void onBizFailure(GenSharingBookEventContext context, FundClearingBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                    if (aggrRoot.isExceededProcessingLimit()) {
                        updateFundPoolActionStatus(context, SharingActionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void onFailure(GenSharingBookEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                    if (aggrRoot.isExceededProcessingLimit()) {
                        updateFundPoolActionStatus(context, SharingActionStatusEnum.FAILED);
                    }
                }

                @Override
                protected void doFinally(GenSharingBookEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

    private void updateFundPoolActionStatus(GenSharingBookEventContext context, SharingActionStatusEnum sharingActionStatusEnum) {
        FundPoolAggrRoot fundPoolAggrRoot = context.getFundPoolAggrRoot();
        fundPoolAggrRoot.updateSharingActionStatus(FundPoolBizDomainVO.SharingActionStatusVO.builder()
                .sharingBookStatus(sharingActionStatusEnum)
                .build());
        fundPoolDomainRepository.save(fundPoolAggrRoot);
    }


    private void genAndSaveFundPoolSharingBook(GenSharingBookEventContext context) {
        int pageSize = 100;
        String lastId = context.getLastId();
        FundPoolAggrRoot pool = context.getFundPoolAggrRoot();
        while (true) {
            List<FundPoolSharingTransactionAggrRoot> transactionAggrRoots = transactionDomainRepository.batchQuery(
                    FundPoolSharingTransactionAggrQuery.builder()
                            .fundPoolId(pool.getId())
                            .status(FundPoolSharingTransactionStatusEnum.PENDING_SETTLEMENT.getCode())
                            .querySize(pageSize)
                            .cursorField(FundTransactionCursorModel.FundTransactionCursorFieldEnum.ID.getInnerField())
                            .endCursor(lastId)
                            .sortField(FundTransactionSortModel.FundTransactionSortFieldEnum.ID.getInnerField())
                            .isDesc(Boolean.TRUE)
                            .build()
            );
            if (CollectionUtils.isEmpty(transactionAggrRoots)) {
                break;
            }
            saveBookAndFlow(transactionAggrRoots);
            if (transactionAggrRoots.size() < pageSize) {
                break;
            }
            lastId = transactionAggrRoots.getLast().getId().toString();
        }
        pool.updateStatus(FundPoolStatusEnum.PENDING_SHARING_SETTLEMENT);
    }

    private void saveBookAndFlow(List<FundPoolSharingTransactionAggrRoot> transactions) {
        List<FundPoolSharingTransBookAggrRoot> sharingBooks = new ArrayList<>();
        List<FundPoolFlowAggrRoot> fundPoolFlows = new ArrayList<>();
        for (FundPoolSharingTransactionAggrRoot transaction : transactions) {
            sharingBooks.addAll(generateSharingBooks(transaction));
            fundPoolFlows.add(getFundPoolFlowAggrRoot(transaction));
        }
        transactionTemplate.executeWithoutResult(status -> {
            fundPoolSharingTransBookDomainRepository.batchSave(sharingBooks);
            fundPoolFlowDomainRepository.batchSave(fundPoolFlows);
        });
    }

    private List<FundPoolSharingTransBookAggrRoot> generateSharingBooks(
            FundPoolSharingTransactionAggrRoot transaction) {
        if (CollectionUtils.isEmpty(transaction.getReceivers())) {
            return List.of();
        }

        // 批量查询所有需要的receiver
        List<Long> receiverIds = transaction.getReceivers()
                .stream()
                .map(ReceiverVO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 找出缓存中不存在的接收方ID
        List<Long> missingReceiverIds = receiverIds.stream()
                .filter(id -> !receiverCache.containsKey(String.valueOf(id)))
                .collect(Collectors.toList());

        // 只查询缓存中不存在的接收方
        if (!missingReceiverIds.isEmpty()) {
            List<ReceiverAggrRoot> newReceivers = receiverDomainRepository.batchQuery(ReceiverAggrQuery.builder()
                    .ids(missingReceiverIds)
                    .build());

            // 将新查询的结果放入缓存
            newReceivers.forEach(receiver -> receiverCache.put(receiver.getIdStr(), receiver));
        }

        List<FundPoolSharingTransBookAggrRoot> sharingBooks = new ArrayList<>();
        for (ReceiverVO receiver : transaction.getReceivers()) {
            ReceiverAggrRoot receiverAggrRoot = receiverCache.get(String.valueOf(receiver.getId()));
            if (Objects.isNull(receiverAggrRoot)) {
                log.error("[分账接收方]>>>>>>接收方不存在, receiverId: {}", receiver.getId());
                continue;
            }
            FundClearingBillAccountModel model = null;
            if (Objects.equals(receiverAggrRoot.getType(), AccountTypeEnum.BRAND)) {
                model = FundClearingBillAccountModel.builder()
                        .type(receiverAggrRoot.getType().getCode())
                        .brandSn(receiverAggrRoot.getSn())
                        .build();
            } else if (Objects.equals(receiverAggrRoot.getType(), AccountTypeEnum.MERCHANT)) {
                model = FundClearingBillAccountModel.builder()
                        .type(receiverAggrRoot.getType().getCode())
                        .merchantSn(receiverAggrRoot.getSn())
                        .build();
            } else if (Objects.equals(receiverAggrRoot.getType(), AccountTypeEnum.STORE)) {
                model = FundClearingBillAccountModel.builder()
                        .type(receiverAggrRoot.getType().getCode())
                        .storeSn(receiverAggrRoot.getSn())
                        .build();
            }

            ClearingAccountSupportService.AccountInfoResult accountInfoResult = clearingAccountSupportService.processAccountInfo(model);

            FundPoolSharingTransBookAggrRoot sharingBook = FundPoolSharingTransBookAggrRootFactory.builder()
                    .coreBuilder()
                    .id(DefaultSerialGenerator.getInstance().genFundPoolSharingTransBookId())
                    .transactionId(transaction.getId())
                    .brandSn(transaction.getBrandSn())
                    .merchantSn(transaction.getMerchantSn())
                    .storeSn(transaction.getStoreSn())
                    .type(transaction.getType())
                    .payeeType(transaction.getPayeeType())
                    .payeeSn(transaction.getPayeeSn())
                    .payeeInfo(transaction.getPayeeInfo())
                    .receiverType(receiverAggrRoot.getType())
                    .receiverSn(receiverAggrRoot.getSn())
                    .receiverInfo(accountInfoResult.buildPayeeInfoVO())
                    .amount(FundPoolSharingTransBookAmountVO.builder()
                            .originAmount(transaction.getAmount().getOriginAmount())
                            .fee(transaction.getAmount().getFee())
                            .sharingBaseAmount(transaction.getSharingBaseAmount())
                            .sharingAmount(receiver.getSharingAmount())
                            .build())
                    .status(FundPoolSharingTransBookStatusEnum.PENDING)
                    .build();
            sharingBooks.add(sharingBook);
        }
        return sharingBooks;
    }

    private static FundPoolFlowAggrRoot getFundPoolFlowAggrRoot(FundPoolSharingTransactionAggrRoot transaction) {
        return FundPoolFlowAggrRootFactory.builder()
                .coreBuilder()
                .id(DefaultSerialGenerator.getInstance().genFundPoolFlowId())
                .fundPoolId(transaction.getFundPoolId())
                .type(FundPoolFlowTypeEnum.CLEARING)
                .amount(FundPoolFlowAmountVO.builder()
                        .transAmount(transaction.getAmount().getSharingAmount())
                        .build())
                .businessType(FundPoolFlowBusinessTypeEnum.CLEARING)
                .businessSn(transaction.getIdStr())
                .status(FundPoolFlowStatusEnum.SUCCESS)
                .bizDomain(FundPoolFlowBizDomainVO.newEmptyInstance())
                .build();
    }

}
